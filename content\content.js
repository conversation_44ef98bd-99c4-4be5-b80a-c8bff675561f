/**
 * 內容腳本 - 拖拽文字檢測和標籤選擇
 */

class DragTextClassifier {
  constructor() {
    this.isDragging = false;
    this.selectedText = '';
    this.dragStartPosition = { x: 0, y: 0 };
    this.tagSelector = null;
    this.tags = [];
    this.settings = {};
    
    this.init();
  }

  async init() {
    try {
      // 載入儲存的數據
      await this.loadData();
      
      // 綁定事件監聽器
      this.bindEvents();
      
      console.log('DragTextClassifier initialized');
    } catch (error) {
      console.error('Failed to initialize DragTextClassifier:', error);
    }
  }

  async loadData() {
    // 從背景腳本獲取數據
    const response = await chrome.runtime.sendMessage({
      action: 'getData',
      type: 'all'
    });
    
    if (response && response.success) {
      this.tags = response.data.tags || [];
      this.settings = response.data.settings || {};
    }
  }

  bindEvents() {
    // 監聽文字選擇
    document.addEventListener('mousedown', this.handleMouseDown.bind(this));
    document.addEventListener('mousemove', this.handleMouseMove.bind(this));
    document.addEventListener('mouseup', this.handleMouseUp.bind(this));
    
    // 監聽鍵盤事件
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
    
    // 監聽來自背景腳本的消息
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));
  }

  handleMouseDown(event) {
    this.dragStartPosition = { x: event.clientX, y: event.clientY };
    this.hideTagSelector();
  }

  handleMouseMove(event) {
    if (!this.isDragging) {
      const distance = Math.sqrt(
        Math.pow(event.clientX - this.dragStartPosition.x, 2) +
        Math.pow(event.clientY - this.dragStartPosition.y, 2)
      );
      
      // 檢測是否開始拖拽（移動距離超過閾值）
      if (distance > 10) {
        this.isDragging = true;
      }
    }
  }

  handleMouseUp(event) {
    if (this.isDragging) {
      this.isDragging = false;
      
      // 獲取選中的文字
      const selection = window.getSelection();
      const selectedText = selection.toString().trim();
      
      if (selectedText && selectedText.length > 0) {
        this.selectedText = selectedText;
        this.showTagSelector(event.clientX, event.clientY);
      }
    }
  }

  handleKeyDown(event) {
    // ESC鍵隱藏標籤選擇器
    if (event.key === 'Escape') {
      this.hideTagSelector();
    }
  }

  handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'updateTags':
        this.tags = request.tags;
        if (this.tagSelector) {
          this.updateTagSelectorContent();
        }
        break;
      case 'updateSettings':
        this.settings = request.settings;
        break;
    }
  }

  showTagSelector(x, y) {
    if (this.tagSelector) {
      this.hideTagSelector();
    }

    // 創建標籤選擇器容器
    this.tagSelector = document.createElement('div');
    this.tagSelector.className = 'drag-text-tag-selector';
    this.tagSelector.innerHTML = this.generateTagSelectorHTML();

    // 設定位置
    this.tagSelector.style.left = `${x + 10}px`;
    this.tagSelector.style.top = `${y - 10}px`;

    // 添加到頁面
    document.body.appendChild(this.tagSelector);

    // 綁定事件
    this.bindTagSelectorEvents();

    // 調整位置以確保不超出視窗
    this.adjustSelectorPosition();
  }

  generateTagSelectorHTML() {
    const tagsHTML = this.tags.map(tag => `
      <div class="tag-item" data-tag-id="${tag.id}" style="border-left: 4px solid ${tag.color}">
        <span class="tag-icon">${tag.icon}</span>
        <span class="tag-name">${tag.name}</span>
      </div>
    `).join('');

    return `
      <div class="tag-selector-header">
        <span class="selected-text-preview">"${this.selectedText.substring(0, 50)}${this.selectedText.length > 50 ? '...' : ''}"</span>
        <button class="close-btn" title="關閉">×</button>
      </div>
      <div class="tag-list">
        ${tagsHTML}
        <div class="tag-item new-tag-item">
          <span class="tag-icon">➕</span>
          <span class="tag-name">建立新標籤</span>
        </div>
      </div>
    `;
  }

  bindTagSelectorEvents() {
    // 標籤點擊事件
    this.tagSelector.querySelectorAll('.tag-item:not(.new-tag-item)').forEach(item => {
      item.addEventListener('click', (event) => {
        const tagId = event.currentTarget.dataset.tagId;
        this.selectTag(tagId);
      });
    });

    // 新建標籤事件
    const newTagItem = this.tagSelector.querySelector('.new-tag-item');
    if (newTagItem) {
      newTagItem.addEventListener('click', () => {
        this.showNewTagDialog();
      });
    }

    // 關閉按鈕事件
    const closeBtn = this.tagSelector.querySelector('.close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.hideTagSelector();
      });
    }

    // 點擊外部關閉
    document.addEventListener('click', this.handleOutsideClick.bind(this), true);
  }

  handleOutsideClick(event) {
    if (this.tagSelector && !this.tagSelector.contains(event.target)) {
      this.hideTagSelector();
    }
  }

  async selectTag(tagId) {
    try {
      // 收集文字數據
      const collectionData = {
        text: this.selectedText,
        tagId: tagId,
        url: window.location.href,
        title: document.title,
        timestamp: new Date().toISOString()
      };

      // 發送到背景腳本保存
      const response = await chrome.runtime.sendMessage({
        action: 'saveCollection',
        data: collectionData
      });

      if (response && response.success) {
        this.showSuccessNotification();
        this.hideTagSelector();
        
        // 清除文字選擇
        window.getSelection().removeAllRanges();
      } else {
        this.showErrorNotification('保存失敗');
      }
    } catch (error) {
      console.error('Failed to select tag:', error);
      this.showErrorNotification('保存失敗');
    }
  }

  showNewTagDialog() {
    const tagName = prompt('請輸入新標籤名稱：');
    if (tagName && tagName.trim()) {
      this.createNewTag(tagName.trim());
    }
  }

  async createNewTag(tagName) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'createTag',
        data: {
          name: tagName,
          color: this.getRandomColor(),
          icon: '🏷️'
        }
      });

      if (response && response.success) {
        // 更新本地標籤列表
        this.tags = response.data.tags;
        
        // 自動選擇新建的標籤
        this.selectTag(response.data.newTag.id);
      } else {
        this.showErrorNotification('建立標籤失敗');
      }
    } catch (error) {
      console.error('Failed to create new tag:', error);
      this.showErrorNotification('建立標籤失敗');
    }
  }

  getRandomColor() {
    const colors = ['#6366f1', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  adjustSelectorPosition() {
    if (!this.tagSelector) return;

    const rect = this.tagSelector.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 調整水平位置
    if (rect.right > viewportWidth) {
      this.tagSelector.style.left = `${viewportWidth - rect.width - 10}px`;
    }

    // 調整垂直位置
    if (rect.bottom > viewportHeight) {
      this.tagSelector.style.top = `${viewportHeight - rect.height - 10}px`;
    }
  }

  hideTagSelector() {
    if (this.tagSelector) {
      document.removeEventListener('click', this.handleOutsideClick, true);
      this.tagSelector.remove();
      this.tagSelector = null;
    }
  }

  showSuccessNotification() {
    this.showNotification('文字已成功收集！', 'success');
  }

  showErrorNotification(message) {
    this.showNotification(message, 'error');
  }

  showNotification(message, type = 'info') {
    if (!this.settings.showNotifications) return;

    const notification = document.createElement('div');
    notification.className = `drag-text-notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // 自動移除通知
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 3000);
  }

  updateTagSelectorContent() {
    if (this.tagSelector) {
      const tagList = this.tagSelector.querySelector('.tag-list');
      if (tagList) {
        const tagsHTML = this.tags.map(tag => `
          <div class="tag-item" data-tag-id="${tag.id}" style="border-left: 4px solid ${tag.color}">
            <span class="tag-icon">${tag.icon}</span>
            <span class="tag-name">${tag.name}</span>
          </div>
        `).join('') + `
          <div class="tag-item new-tag-item">
            <span class="tag-icon">➕</span>
            <span class="tag-name">建立新標籤</span>
          </div>
        `;
        tagList.innerHTML = tagsHTML;
        this.bindTagSelectorEvents();
      }
    }
  }
}

// 初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new DragTextClassifier();
  });
} else {
  new DragTextClassifier();
}
