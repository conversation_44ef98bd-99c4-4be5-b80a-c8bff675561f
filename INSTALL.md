# DragTextClassified 安裝指南

## 📦 安裝步驟

### 開發者模式安裝（推薦）

1. **下載專案**
   ```bash
   git clone <repository-url>
   cd DragTextClassified
   ```

2. **開啟Chrome擴展管理頁面**
   - 在Chrome瀏覽器中輸入：`chrome://extensions/`
   - 或者：選單 → 更多工具 → 擴展程式

3. **啟用開發者模式**
   - 點擊右上角的「開發者模式」切換開關

4. **載入擴展**
   - 點擊「載入未封裝項目」按鈕
   - 選擇 `DragTextClassified` 資料夾
   - 點擊「選擇資料夾」

5. **確認安裝**
   - 擴展應該出現在擴展列表中
   - 瀏覽器工具列會顯示擴展圖示

## 🚀 首次使用

### 基本設定
1. 點擊擴展圖示開啟彈出視窗
2. 系統會自動建立預設標籤
3. 可以在設定頁面自定義行為

### 測試功能
1. 開啟 `test.html` 文件
2. 選中任意文字並拖拽
3. 觀察標籤選擇器是否出現
4. 選擇標籤完成收集

## 🎯 使用方法

### 收集文字
1. **拖拽收集**：選中文字並拖拽，選擇標籤
2. **右鍵收集**：右鍵選中文字，選擇「收集選中文字」
3. **快捷鍵**：`Ctrl+Shift+T` 快速顯示標籤選擇器

### 管理收集
1. **查看收集**：點擊擴展圖示
2. **搜尋篩選**：使用搜尋框和標籤篩選
3. **匯出數據**：在設定頁面匯出為JSON

### 標籤管理
1. **新增標籤**：在彈出視窗或拖拽時建立
2. **編輯標籤**：在彈出視窗中編輯名稱、顏色、圖示
3. **刪除標籤**：在彈出視窗中刪除不需要的標籤

## ⚙️ 設定選項

### 一般設定
- **主題**：淺色/深色/跟隨系統
- **自動儲存**：拖拽時自動儲存
- **顯示通知**：收集成功時顯示通知

### 拖拽設定
- **靈敏度**：調整拖拽檢測距離
- **選擇器位置**：標籤選擇器顯示位置
- **最小長度**：觸發收集的最小文字長度

### 快捷鍵
- `Ctrl+Shift+D`：開啟收集界面
- `Ctrl+Shift+T`：快速標籤選擇
- `Esc`：關閉標籤選擇器

## 🔧 故障排除

### 常見問題

**Q: 拖拽文字時沒有出現標籤選擇器**
A: 
- 確認擴展已啟用
- 檢查網頁是否完全載入
- 嘗試重新載入頁面
- 檢查瀏覽器控制台錯誤

**Q: 無法儲存收集的文字**
A:
- 檢查擴展權限
- 確認儲存空間未滿
- 嘗試重新安裝擴展

**Q: 彈出視窗無法開啟**
A:
- 檢查popup文件是否存在
- 查看擴展管理頁面的錯誤訊息
- 重新載入擴展

**Q: 設定無法儲存**
A:
- 確認有storage權限
- 檢查Chrome同步設定
- 嘗試清除擴展數據後重新設定

### 除錯步驟

1. **檢查擴展狀態**
   - 前往 `chrome://extensions/`
   - 確認擴展已啟用且無錯誤

2. **查看控制台**
   - 按 F12 開啟開發者工具
   - 查看 Console 標籤的錯誤訊息

3. **檢查權限**
   - 確認擴展有必要的權限
   - 檢查是否被其他擴展衝突

4. **重新安裝**
   - 移除擴展
   - 重新載入專案資料夾

## 📊 性能優化

### 建議設定
- 設定合適的拖拽靈敏度
- 定期清理不需要的收集項目
- 使用標籤篩選提高查找效率

### 數據管理
- 定期匯出重要數據
- 清理過期的收集項目
- 合併相似的標籤

## 🔒 隱私和安全

### 數據儲存
- 所有數據儲存在本地Chrome儲存空間
- 不會上傳到任何外部伺服器
- 支援Chrome同步（可選）

### 權限說明
- `activeTab`：讀取當前頁面內容
- `storage`：儲存用戶數據
- `contextMenus`：右鍵選單功能
- `scripting`：注入內容腳本

## 📞 支援

### 獲取幫助
- 查看 [README.md](README.md) 了解更多資訊
- 在 GitHub 提交 Issue
- 查看 [測試頁面](test.html) 驗證功能

### 回報問題
請提供以下資訊：
- Chrome版本
- 擴展版本
- 錯誤訊息截圖
- 重現步驟

---

**注意**：此擴展遵循Chrome Web Store政策，確保用戶隱私和數據安全。
