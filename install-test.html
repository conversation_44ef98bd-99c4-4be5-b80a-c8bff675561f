<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DragTextClassified 安裝測試</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .error {
            background: #fee2e2;
            color: #dc2626;
            border: 1px solid #ef4444;
        }
        
        .warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        
        .test-area {
            background: #f0f9ff;
            border: 2px dashed #3b82f6;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin: 20px 0;
            user-select: text;
            cursor: text;
        }
        
        .test-area:hover {
            background: #e0f2fe;
        }
        
        .instructions {
            background: #eff6ff;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border-left: 3px solid #10b981;
        }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        button:hover {
            background: #2563eb;
        }
        
        .code {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🏷️ DragTextClassified 安裝測試</h1>
    
    <div class="instructions">
        <h2>📋 安裝步驟</h2>
        <div class="step">
            <strong>1.</strong> 確保 <code>logo.png</code> 文件存在於專案根目錄
        </div>
        <div class="step">
            <strong>2.</strong> 開啟 Chrome 瀏覽器，前往 <code>chrome://extensions/</code>
        </div>
        <div class="step">
            <strong>3.</strong> 開啟右上角的「開發者模式」
        </div>
        <div class="step">
            <strong>4.</strong> 點擊「載入未封裝項目」
        </div>
        <div class="step">
            <strong>5.</strong> 選擇 DragTextClassified 專案資料夾
        </div>
        <div class="step">
            <strong>6.</strong> 確認擴展出現在列表中且已啟用
        </div>
    </div>

    <h2>🔍 安裝狀態檢查</h2>
    <div id="extensionStatus" class="status warning">
        正在檢查擴展狀態...
    </div>

    <h2>🎯 功能測試</h2>
    <div class="test-area" id="testArea">
        <h3>拖拽測試區域</h3>
        <p><strong>選中這段文字並拖拽它！</strong></p>
        <p>如果擴展正常工作，應該會出現標籤資料夾讓您拖放文字。</p>
        <p>這是另一段測試文字：「DragTextClassified 是一個強大的文字收集工具」</p>
    </div>

    <div id="testResults"></div>

    <h2>🛠️ 手動測試</h2>
    <button onclick="testExtensionAPI()">測試擴展 API</button>
    <button onclick="testStorage()">測試儲存功能</button>
    <button onclick="openTestPage()">開啟完整測試頁面</button>

    <h2>📊 測試結果</h2>
    <div id="testLog"></div>

    <h2>❓ 故障排除</h2>
    <div class="instructions">
        <h3>常見問題：</h3>
        <ul>
            <li><strong>擴展未出現：</strong>檢查是否選擇了正確的資料夾</li>
            <li><strong>載入錯誤：</strong>檢查 manifest.json 語法是否正確</li>
            <li><strong>圖示未顯示：</strong>確認 logo.png 文件存在</li>
            <li><strong>功能無效：</strong>檢查瀏覽器控制台錯誤訊息</li>
        </ul>
        
        <h3>檢查清單：</h3>
        <div class="code">
            ✅ logo.png 文件存在<br>
            ✅ manifest.json 語法正確<br>
            ✅ 所有必要文件都在專案中<br>
            ✅ Chrome 開發者模式已啟用<br>
            ✅ 擴展已成功載入<br>
            ✅ 沒有控制台錯誤
        </div>
    </div>

    <script>
        let testLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testLog.push(`[${timestamp}] ${message}`);
            
            const logDiv = document.getElementById('testLog');
            const entry = document.createElement('div');
            entry.className = `status ${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            
            console.log(`[DragTextClassified Test] ${message}`);
        }

        function checkExtensionStatus() {
            const statusDiv = document.getElementById('extensionStatus');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                try {
                    // 嘗試發送消息到擴展
                    chrome.runtime.sendMessage({action: 'ping'}, (response) => {
                        if (chrome.runtime.lastError) {
                            statusDiv.className = 'status error';
                            statusDiv.textContent = '❌ 擴展未安裝或未啟用';
                            log('擴展未安裝或未啟用', 'error');
                        } else {
                            statusDiv.className = 'status success';
                            statusDiv.textContent = '✅ 擴展已安裝並正常運行';
                            log('擴展已安裝並正常運行', 'success');
                        }
                    });
                } catch (error) {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '❌ 無法連接到擴展';
                    log('無法連接到擴展: ' + error.message, 'error');
                }
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Chrome 擴展 API 不可用';
                log('Chrome 擴展 API 不可用', 'error');
            }
        }

        function testExtensionAPI() {
            log('開始測試擴展 API...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({
                    action: 'getData',
                    type: 'all'
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        log('API 測試失敗: ' + chrome.runtime.lastError.message, 'error');
                    } else if (response && response.success) {
                        log('API 測試成功，數據載入正常', 'success');
                        log(`標籤數量: ${response.data.tags ? response.data.tags.length : 0}`);
                        log(`收集數量: ${response.data.collections ? response.data.collections.length : 0}`);
                    } else {
                        log('API 測試失敗: 無效回應', 'error');
                    }
                });
            } else {
                log('無法測試 API: Chrome 擴展 API 不可用', 'error');
            }
        }

        function testStorage() {
            log('開始測試儲存功能...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({
                    action: 'createTag',
                    data: {
                        name: '測試標籤',
                        color: '#3b82f6',
                        icon: '🧪'
                    }
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        log('儲存測試失敗: ' + chrome.runtime.lastError.message, 'error');
                    } else if (response && response.success) {
                        log('儲存測試成功，標籤建立正常', 'success');
                    } else {
                        log('儲存測試失敗: 無法建立標籤', 'error');
                    }
                });
            } else {
                log('無法測試儲存: Chrome 擴展 API 不可用', 'error');
            }
        }

        function openTestPage() {
            window.open('test.html', '_blank');
        }

        // 監聽拖拽事件
        document.addEventListener('dragstart', (e) => {
            log('檢測到拖拽開始事件', 'success');
        });

        document.addEventListener('dragend', (e) => {
            log('檢測到拖拽結束事件');
        });

        // 頁面載入時檢查狀態
        window.addEventListener('load', () => {
            log('測試頁面載入完成');
            setTimeout(checkExtensionStatus, 1000);
        });

        // 定期檢查擴展狀態
        setInterval(checkExtensionStatus, 10000);
    </script>
</body>
</html>
