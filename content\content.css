/**
 * 拖拽文字分類器樣式
 */

/* 標籤資料夾容器 */
.drag-text-tag-folders {
  position: fixed;
  z-index: 999999;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  animation: fadeInScale 0.3s ease-out;
  width: 520px;
  height: 360px;
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

/* 拖放容器 */
.drag-drop-container {
  display: flex;
  height: 100%;
}

/* 左側拖放區域 */
.drop-zone {
  flex: 1;
  background: #f8fafc;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  position: relative;
  transition: all 0.3s ease;
}

.drop-zone.drag-over {
  background: #eff6ff;
  border-right-color: #3b82f6;
}

.drop-zone.awaiting-selection {
  background: #fef3c7;
  border-right-color: #f59e0b;
}

.drop-zone-icon {
  color: #9ca3af;
  margin-bottom: 16px;
  transition: color 0.3s ease;
}

.drop-zone.drag-over .drop-zone-icon {
  color: #3b82f6;
}

.drop-zone-text {
  text-align: center;
  color: #6b7280;
}

.drop-zone-text h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.drop-zone-text p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.selected-text-preview {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  font-size: 12px;
  color: #6b7280;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 右側資料夾側邊欄 */
.folders-sidebar {
  width: 240px;
  background: #ffffff;
  border-left: 1px solid #e5e7eb;
}

.folders-list {
  padding: 0;
  margin: 0;
}

/* 資料夾項目 */
.folder-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.folder-item:hover {
  background: #f8fafc;
}

.folder-item.drag-over {
  background: #eff6ff;
  border-left: 3px solid #3b82f6;
}

.folder-item.selectable {
  background: #fef3c7;
  animation: highlight 1s ease-in-out infinite;
}

@keyframes highlight {
  0%, 100% { background: #fef3c7; }
  50% { background: #fde68a; }
}

.folder-icon {
  font-size: 16px;
  margin-right: 12px;
  color: #6b7280;
  width: 20px;
  text-align: center;
}

.folder-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 新建資料夾 */
.folder-item.new-folder {
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  color: #6b7280;
}

.folder-item.new-folder:hover {
  background: #f3f4f6;
  color: #374151;
}

.folder-item.new-folder .folder-icon {
  color: #9ca3af;
}

.folder-item.new-folder:hover .folder-icon {
  color: #6b7280;
}

/* 標籤選擇器容器（保留原有功能） */
.drag-text-tag-selector {
  position: fixed;
  z-index: 999999;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  min-width: 280px;
  max-width: 320px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  animation: fadeInScale 0.2s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 標籤選擇器標題 */
.tag-selector-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f9fafb;
  border-radius: 12px 12px 0 0;
}

.selected-text-preview {
  color: #6b7280;
  font-size: 12px;
  font-style: italic;
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #9ca3af;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #6b7280;
}

/* 標籤列表 */
.tag-list {
  padding: 8px 0;
  max-height: 300px;
  overflow-y: auto;
}

/* 標籤項目 */
.tag-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s;
  border-left: 4px solid transparent;
}

.tag-item:hover {
  background: #f9fafb;
}

.tag-item:active {
  background: #f3f4f6;
}

.tag-icon {
  margin-right: 10px;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.tag-name {
  color: #374151;
  font-weight: 500;
  flex: 1;
}

/* 新建標籤項目 */
.new-tag-item {
  border-top: 1px solid #f3f4f6;
  margin-top: 4px;
  color: #6366f1;
}

.new-tag-item:hover {
  background: #f0f9ff;
}

.new-tag-item .tag-name {
  color: #6366f1;
  font-weight: 600;
}

/* 通知樣式 */
.drag-text-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000000;
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.drag-text-notification.success {
  background: #10b981;
}

.drag-text-notification.error {
  background: #ef4444;
}

.drag-text-notification.info {
  background: #6366f1;
}

/* 深色主題支援 */
@media (prefers-color-scheme: dark) {
  .drag-text-tag-selector {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .tag-selector-header {
    background: #111827;
    border-bottom-color: #374151;
  }
  
  .selected-text-preview {
    color: #9ca3af;
  }
  
  .close-btn {
    color: #6b7280;
  }
  
  .close-btn:hover {
    background: #374151;
    color: #d1d5db;
  }
  
  .tag-item:hover {
    background: #374151;
  }
  
  .tag-item:active {
    background: #4b5563;
  }
  
  .tag-name {
    color: #f9fafb;
  }
  
  .new-tag-item {
    border-top-color: #374151;
  }
  
  .new-tag-item:hover {
    background: #1e3a8a;
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .drag-text-tag-folders {
    width: 90vw;
    height: 70vh;
  }

  .drag-drop-container {
    flex-direction: column;
  }

  .drop-zone {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
    padding: 20px;
  }

  .folders-sidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid #e5e7eb;
  }

  .drop-zone-text h3 {
    font-size: 16px;
  }

  .drop-zone-text p {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .drag-text-tag-selector {
    min-width: 240px;
    max-width: 280px;
  }

  .tag-item {
    padding: 12px 16px;
  }

  .tag-icon {
    font-size: 14px;
    width: 18px;
  }

  .tag-name {
    font-size: 13px;
  }

  .drag-text-tag-folders {
    width: 95vw;
    height: 80vh;
  }

  .drop-zone {
    padding: 16px;
  }

  .folder-item {
    padding: 10px 12px;
  }

  .folder-name {
    font-size: 13px;
  }
}

/* 滾動條樣式 */
.tag-list::-webkit-scrollbar {
  width: 6px;
}

.tag-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.tag-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.tag-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 深色主題滾動條 */
@media (prefers-color-scheme: dark) {
  .tag-list::-webkit-scrollbar-track {
    background: #374151;
  }
  
  .tag-list::-webkit-scrollbar-thumb {
    background: #6b7280;
  }
  
  .tag-list::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
}
