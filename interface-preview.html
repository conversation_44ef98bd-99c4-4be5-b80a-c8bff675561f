<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DragTextClassified 界面預覽</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .preview-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 1px solid #e5e7eb;
            width: 520px;
            height: 360px;
            overflow: hidden;
            position: relative;
        }

        .drag-drop-container {
            display: flex;
            height: 100%;
        }

        .drop-zone {
            flex: 1;
            background: #f8fafc;
            border-right: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            position: relative;
        }

        .drop-zone-icon {
            color: #9ca3af;
            margin-bottom: 16px;
        }

        .drop-zone-text {
            text-align: center;
            color: #6b7280;
        }

        .drop-zone-text h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
            color: #374151;
        }

        .drop-zone-text p {
            margin: 0;
            font-size: 14px;
            color: #6b7280;
        }

        .selected-text-preview {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 12px;
            font-size: 12px;
            color: #6b7280;
        }

        .folders-sidebar {
            width: 240px;
            background: #ffffff;
            border-left: 1px solid #e5e7eb;
        }

        .folder-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .folder-item:hover {
            background: #f8fafc;
        }

        .folder-icon {
            font-size: 16px;
            margin-right: 12px;
            color: #6b7280;
            width: 20px;
            text-align: center;
        }

        .folder-name {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            flex: 1;
        }

        .folder-item.new-folder {
            border-top: 1px solid #e5e7eb;
            background: #f9fafb;
            color: #6b7280;
        }

        .description {
            max-width: 600px;
            margin: 20px auto;
            text-align: center;
            color: #6b7280;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            max-width: 800px;
            margin: 40px auto;
        }

        .feature {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .feature h3 {
            margin: 0 0 10px 0;
            color: #374151;
        }

        .feature p {
            margin: 0;
            color: #6b7280;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div>
        <div class="preview-container">
            <div class="drag-drop-container">
                <div class="drop-zone">
                    <div class="drop-zone-icon">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10,9 9,9 8,9"/>
                        </svg>
                    </div>
                    <div class="drop-zone-text">
                        <h3>Drag and drop files here</h3>
                        <p>拖拽文字到此區域或選擇右側資料夾</p>
                    </div>
                    <div class="selected-text-preview">
                        "這是一段被選中的測試文字，將會顯示在這裡..."
                    </div>
                </div>
                <div class="folders-sidebar">
                    <div class="folder-item">
                        <div class="folder-icon">📁</div>
                        <span class="folder-name">webdesign</span>
                    </div>
                    <div class="folder-item">
                        <div class="folder-icon">📁</div>
                        <span class="folder-name">shoes</span>
                    </div>
                    <div class="folder-item">
                        <div class="folder-icon">📁</div>
                        <span class="folder-name">LLM</span>
                    </div>
                    <div class="folder-item">
                        <div class="folder-icon">📁</div>
                        <span class="folder-name">APP</span>
                    </div>
                    <div class="folder-item">
                        <div class="folder-icon">📁</div>
                        <span class="folder-name">Logo</span>
                    </div>
                    <div class="folder-item">
                        <div class="folder-icon">📁</div>
                        <span class="folder-name">HexagonMedia</span>
                    </div>
                    <div class="folder-item">
                        <div class="folder-icon">📁</div>
                        <span class="folder-name">wedding</span>
                    </div>
                    <div class="folder-item new-folder">
                        <div class="folder-icon">➕</div>
                        <span class="folder-name">Create or select folder</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="description">
            <h2>🏷️ DragTextClassified 新界面設計</h2>
            <p>完全按照您的要求實現的檔案管理器風格拖放界面</p>
        </div>

        <div class="features">
            <div class="feature">
                <h3>📂 左側拖放區域</h3>
                <p>顯示文件圖示和拖放提示，底部預覽選中的文字內容</p>
            </div>
            <div class="feature">
                <h3>📋 右側資料夾列表</h3>
                <p>清晰列出所有標籤資料夾，支援拖放和點擊操作</p>
            </div>
            <div class="feature">
                <h3>🎯 直觀操作</h3>
                <p>可以拖放到左側區域或直接拖放到右側特定資料夾</p>
            </div>
            <div class="feature">
                <h3>➕ 新增標籤</h3>
                <p>底部的「Create or select folder」項目用於建立新標籤</p>
            </div>
        </div>
    </div>
</body>
</html>
