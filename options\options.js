/**
 * 設定頁面功能
 */

class OptionsManager {
  constructor() {
    this.settings = {};
    this.init();
  }

  async init() {
    try {
      // 載入設定
      await this.loadSettings();
      
      // 綁定事件
      this.bindEvents();
      
      // 渲染界面
      this.renderSettings();
      
      console.log('Options page initialized');
    } catch (error) {
      console.error('Failed to initialize options page:', error);
    }
  }

  async loadSettings() {
    const response = await chrome.runtime.sendMessage({
      action: 'getData',
      type: 'settings'
    });
    
    if (response && response.success) {
      this.settings = response.data.settings || {};
    }
  }

  bindEvents() {
    // 設定變更事件
    document.getElementById('theme').addEventListener('change', this.handleSettingChange.bind(this));
    document.getElementById('autoSave').addEventListener('change', this.handleSettingChange.bind(this));
    document.getElementById('showNotifications').addEventListener('change', this.handleSettingChange.bind(this));
    document.getElementById('dragSensitivity').addEventListener('change', this.handleSettingChange.bind(this));
    document.getElementById('tagSelectorPosition').addEventListener('change', this.handleSettingChange.bind(this));
    document.getElementById('minTextLength').addEventListener('change', this.handleSettingChange.bind(this));

    // 數據管理事件
    document.getElementById('exportData').addEventListener('click', this.exportData.bind(this));
    document.getElementById('importData').addEventListener('click', this.selectImportFile.bind(this));
    document.getElementById('importFile').addEventListener('change', this.importData.bind(this));
    document.getElementById('clearAllData').addEventListener('click', this.clearAllData.bind(this));

    // 連結事件
    document.getElementById('openShortcutsPage').addEventListener('click', this.openShortcutsPage.bind(this));
    document.getElementById('viewSource').addEventListener('click', this.viewSource.bind(this));
    document.getElementById('reportIssue').addEventListener('click', this.reportIssue.bind(this));
    document.getElementById('rateExtension').addEventListener('click', this.rateExtension.bind(this));
  }

  renderSettings() {
    // 渲染各個設定項目
    document.getElementById('theme').value = this.settings.theme || 'light';
    document.getElementById('autoSave').checked = this.settings.autoSave !== false;
    document.getElementById('showNotifications').checked = this.settings.showNotifications !== false;
    document.getElementById('dragSensitivity').value = this.settings.dragSensitivity || 'medium';
    document.getElementById('tagSelectorPosition').value = this.settings.tagSelectorPosition || 'cursor';
    document.getElementById('minTextLength').value = this.settings.minTextLength || 3;
  }

  async handleSettingChange(event) {
    const { id, type, checked, value } = event.target;
    
    // 更新本地設定
    if (type === 'checkbox') {
      this.settings[id] = checked;
    } else {
      this.settings[id] = value;
    }
    
    // 儲存設定
    await this.saveSettings();
  }

  async saveSettings() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'updateSettings',
        settings: this.settings
      });
      
      if (response && response.success) {
        this.showSaveStatus();
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }

  showSaveStatus() {
    const status = document.getElementById('saveStatus');
    status.style.display = 'flex';
    
    setTimeout(() => {
      status.style.display = 'none';
    }, 2000);
  }

  async exportData() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'exportData'
      });
      
      if (response && response.success) {
        const dataStr = JSON.stringify(response.data, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `dragtext-export-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        this.showNotification('數據匯出成功', 'success');
      } else {
        this.showNotification('匯出失敗', 'error');
      }
    } catch (error) {
      console.error('Failed to export data:', error);
      this.showNotification('匯出失敗', 'error');
    }
  }

  selectImportFile() {
    document.getElementById('importFile').click();
  }

  async importData(event) {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const text = await file.text();
      const data = JSON.parse(text);
      
      // 驗證數據格式
      if (!this.validateImportData(data)) {
        this.showNotification('無效的數據格式', 'error');
        return;
      }
      
      if (!confirm('匯入數據將覆蓋現有數據，確定要繼續嗎？')) {
        return;
      }
      
      // 匯入數據
      await this.performImport(data);
      
      this.showNotification('數據匯入成功', 'success');
      
      // 重新載入頁面以顯示新數據
      setTimeout(() => {
        window.location.reload();
      }, 1000);
      
    } catch (error) {
      console.error('Failed to import data:', error);
      this.showNotification('匯入失敗：文件格式錯誤', 'error');
    }
    
    // 清除文件選擇
    event.target.value = '';
  }

  validateImportData(data) {
    return data && 
           Array.isArray(data.collections) && 
           Array.isArray(data.tags) &&
           data.version;
  }

  async performImport(data) {
    // 這裡需要實現匯入邏輯
    // 由於我們的儲存系統，需要逐個添加項目
    try {
      // 先清除現有數據
      await chrome.runtime.sendMessage({
        action: 'clearAllData'
      });
      
      // 匯入標籤
      for (const tag of data.tags) {
        await chrome.runtime.sendMessage({
          action: 'createTag',
          data: {
            name: tag.name,
            color: tag.color,
            icon: tag.icon
          }
        });
      }
      
      // 匯入收集項目
      for (const collection of data.collections) {
        await chrome.runtime.sendMessage({
          action: 'saveCollection',
          data: collection
        });
      }
      
    } catch (error) {
      throw new Error('匯入過程中發生錯誤');
    }
  }

  async clearAllData() {
    if (!confirm('這將永久刪除所有收集的文字和標籤，確定要繼續嗎？')) {
      return;
    }
    
    if (!confirm('最後確認：所有數據將無法恢復！')) {
      return;
    }
    
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'clearAllData'
      });
      
      if (response && response.success) {
        this.showNotification('所有數據已清除', 'success');
        
        // 重新載入頁面
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        this.showNotification('清除失敗', 'error');
      }
    } catch (error) {
      console.error('Failed to clear data:', error);
      this.showNotification('清除失敗', 'error');
    }
  }

  openShortcutsPage(event) {
    event.preventDefault();
    chrome.tabs.create({
      url: 'chrome://extensions/shortcuts'
    });
  }

  viewSource(event) {
    event.preventDefault();
    chrome.tabs.create({
      url: 'https://github.com/username/DragTextClassified'
    });
  }

  reportIssue(event) {
    event.preventDefault();
    chrome.tabs.create({
      url: 'https://github.com/username/DragTextClassified/issues'
    });
  }

  rateExtension(event) {
    event.preventDefault();
    chrome.tabs.create({
      url: 'https://chrome.google.com/webstore/detail/dragtextclassified/extension-id'
    });
  }

  showNotification(message, type = 'info') {
    // 創建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
      <span class="notification-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
      <span class="notification-text">${message}</span>
    `;
    
    // 添加樣式
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      zIndex: '1000',
      padding: '12px 20px',
      borderRadius: '8px',
      color: 'white',
      fontWeight: '500',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      animation: 'slideInRight 0.3s ease-out',
      background: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#6366f1'
    });
    
    document.body.appendChild(notification);
    
    // 自動移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 3000);
  }
}

// 添加動畫樣式
const style = document.createElement('style');
style.textContent = `
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(100%);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
`;
document.head.appendChild(style);

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  new OptionsManager();
});
