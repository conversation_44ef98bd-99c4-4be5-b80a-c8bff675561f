<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DragTextClassified</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <!-- 標題欄 -->
    <header class="popup-header">
      <div class="header-content">
        <h1 class="app-title">
          <span class="app-icon">🏷️</span>
          DragTextClassified
        </h1>
        <div class="header-actions">
          <button class="icon-btn" id="settingsBtn" title="設定">
            <span>⚙️</span>
          </button>
          <button class="icon-btn" id="exportBtn" title="匯出數據">
            <span>📤</span>
          </button>
        </div>
      </div>
    </header>

    <!-- 搜尋和篩選 -->
    <div class="search-section">
      <div class="search-container">
        <input type="text" id="searchInput" placeholder="搜尋收集的文字..." class="search-input">
        <button class="search-btn" id="searchBtn">
          <span>🔍</span>
        </button>
      </div>
      <div class="filter-container">
        <select id="tagFilter" class="tag-filter">
          <option value="">所有標籤</option>
        </select>
        <select id="sortBy" class="sort-select">
          <option value="newest">最新優先</option>
          <option value="oldest">最舊優先</option>
          <option value="alphabetical">按字母排序</option>
        </select>
      </div>
    </div>

    <!-- 統計信息 -->
    <div class="stats-section">
      <div class="stat-item">
        <span class="stat-number" id="totalCount">0</span>
        <span class="stat-label">總收集數</span>
      </div>
      <div class="stat-item">
        <span class="stat-number" id="tagCount">0</span>
        <span class="stat-label">標籤數</span>
      </div>
      <div class="stat-item">
        <span class="stat-number" id="todayCount">0</span>
        <span class="stat-label">今日收集</span>
      </div>
    </div>

    <!-- 標籤管理 -->
    <div class="tags-section">
      <div class="section-header">
        <h3>標籤管理</h3>
        <button class="add-tag-btn" id="addTagBtn">
          <span>➕</span> 新增標籤
        </button>
      </div>
      <div class="tags-container" id="tagsContainer">
        <!-- 標籤將動態載入 -->
      </div>
    </div>

    <!-- 收集項目列表 -->
    <div class="collections-section">
      <div class="section-header">
        <h3>收集的文字</h3>
        <div class="collection-actions">
          <button class="icon-btn" id="selectAllBtn" title="全選">
            <span>☑️</span>
          </button>
          <button class="icon-btn" id="deleteSelectedBtn" title="刪除選中" disabled>
            <span>🗑️</span>
          </button>
        </div>
      </div>
      <div class="collections-container" id="collectionsContainer">
        <!-- 收集項目將動態載入 -->
      </div>
    </div>

    <!-- 空狀態 -->
    <div class="empty-state" id="emptyState" style="display: none;">
      <div class="empty-icon">📝</div>
      <h3>還沒有收集任何文字</h3>
      <p>在任何網頁上拖拽文字來開始收集吧！</p>
    </div>

    <!-- 載入狀態 -->
    <div class="loading-state" id="loadingState">
      <div class="loading-spinner"></div>
      <p>載入中...</p>
    </div>
  </div>

  <!-- 新增標籤對話框 -->
  <div class="modal-overlay" id="tagModalOverlay" style="display: none;">
    <div class="modal">
      <div class="modal-header">
        <h3 id="modalTitle">新增標籤</h3>
        <button class="close-btn" id="closeModalBtn">×</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="tagName">標籤名稱</label>
          <input type="text" id="tagName" placeholder="輸入標籤名稱" maxlength="20">
        </div>
        <div class="form-group">
          <label for="tagColor">顏色</label>
          <div class="color-picker">
            <input type="color" id="tagColor" value="#6366f1">
            <div class="color-presets">
              <button class="color-preset" data-color="#6366f1" style="background: #6366f1;"></button>
              <button class="color-preset" data-color="#ef4444" style="background: #ef4444;"></button>
              <button class="color-preset" data-color="#10b981" style="background: #10b981;"></button>
              <button class="color-preset" data-color="#f59e0b" style="background: #f59e0b;"></button>
              <button class="color-preset" data-color="#8b5cf6" style="background: #8b5cf6;"></button>
              <button class="color-preset" data-color="#06b6d4" style="background: #06b6d4;"></button>
            </div>
          </div>
        </div>
        <div class="form-group">
          <label for="tagIcon">圖示</label>
          <div class="icon-picker">
            <input type="text" id="tagIcon" value="🏷️" maxlength="2">
            <div class="icon-presets">
              <button class="icon-preset" data-icon="🏷️">🏷️</button>
              <button class="icon-preset" data-icon="📝">📝</button>
              <button class="icon-preset" data-icon="⭐">⭐</button>
              <button class="icon-preset" data-icon="🔍">🔍</button>
              <button class="icon-preset" data-icon="💬">💬</button>
              <button class="icon-preset" data-icon="✅">✅</button>
              <button class="icon-preset" data-icon="📚">📚</button>
              <button class="icon-preset" data-icon="💡">💡</button>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" id="cancelBtn">取消</button>
        <button class="btn btn-primary" id="saveTagBtn">儲存</button>
      </div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
