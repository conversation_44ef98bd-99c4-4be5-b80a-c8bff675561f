<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Popup 調試頁面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            background: #f9fafb;
        }
        
        .debug-section h3 {
            margin: 0 0 10px 0;
            color: #374151;
        }
        
        .debug-info {
            font-family: monospace;
            background: #1f2937;
            color: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-size: 12px;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 DragTextClassified Popup 調試</h1>
        
        <div class="debug-section">
            <h3>📊 擴展狀態檢查</h3>
            <button class="test-button" onclick="checkExtensionStatus()">檢查擴展狀態</button>
            <button class="test-button" onclick="testBackgroundConnection()">測試背景腳本連接</button>
            <button class="test-button" onclick="checkStorageData()">檢查儲存數據</button>
            <button class="test-button" onclick="checkContentScript()">檢查內容腳本</button>
            <div id="extensionStatus" class="status info">點擊按鈕開始檢查...</div>
        </div>

        <div class="debug-section">
            <h3>ℹ️ 使用說明</h3>
            <div class="status info">
                <strong>重要提醒：</strong><br>
                • 此調試頁面作為普通網頁運行，無法直接訪問擴展 API<br>
                • 要完整測試擴展功能，請：<br>
                　1. 確保擴展已安裝並啟用<br>
                　2. 重新載入此頁面<br>
                　3. 檢查拖拽功能是否正常<br>
                　4. 點擊擴展圖示查看 popup 界面<br>
                • 如需完整 API 測試，請在擴展的 popup 或 options 頁面中進行
            </div>
        </div>
        
        <div class="debug-section">
            <h3>💾 儲存數據測試</h3>
            <button class="test-button" onclick="createTestData()">建立測試數據</button>
            <button class="test-button" onclick="clearAllData()">清除所有數據</button>
            <div id="storageStatus" class="status info">準備測試儲存功能...</div>
        </div>
        
        <div class="debug-section">
            <h3>📝 調試信息</h3>
            <div id="debugInfo" class="debug-info">等待調試信息...</div>
        </div>
        
        <div class="debug-section">
            <h3>🎯 拖拽測試</h3>
            <p>選中下方文字並拖拽，檢查是否出現標籤資料夾：</p>
            <div style="padding: 20px; background: #f3f4f6; border-radius: 6px; margin: 10px 0;">
                <strong>測試文字：</strong>這是一段用於測試拖拽功能的文字。請選中並拖拽它來測試 DragTextClassified 擴展的功能。
            </div>
            <div style="padding: 20px; background: #fef3c7; border-radius: 6px; margin: 10px 0;">
                <strong>另一段測試文字：</strong>這裡有更多的測試內容，包含一些特殊字符和符號！@#$%^&*()_+{}|:"<>?[]\\;',./ 以及中文字符測試。
            </div>
        </div>
    </div>

    <script>
        let debugLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            debugLog.push(logEntry);
            updateDebugInfo();
            console.log(logEntry);
        }
        
        function updateDebugInfo() {
            document.getElementById('debugInfo').textContent = debugLog.slice(-20).join('\n');
        }
        
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        async function checkExtensionStatus() {
            log('檢查擴展狀態...');

            try {
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    log('當前頁面不在擴展環境中運行', 'info');
                    updateStatus('extensionStatus',
                        '⚠️ 此頁面需要在 Chrome 擴展環境中運行。請將此頁面作為擴展的一部分載入，或在有擴展權限的頁面中測試。',
                        'info');

                    // 檢查是否有內容腳本注入
                    if (window.dragTextClassifier) {
                        log('檢測到 DragTextClassified 內容腳本', 'info');
                        updateStatus('extensionStatus', '✅ 檢測到擴展內容腳本，拖拽功能應該可用', 'success');
                    } else {
                        log('未檢測到 DragTextClassified 內容腳本', 'info');
                        updateStatus('extensionStatus', '❌ 未檢測到擴展內容腳本，請確認擴展已安裝並重新載入頁面', 'error');
                    }
                    return;
                }

                const manifest = chrome.runtime.getManifest();
                log(`擴展名稱: ${manifest.name}`);
                log(`擴展版本: ${manifest.version}`);

                updateStatus('extensionStatus', '✅ 擴展已正確載入', 'success');
            } catch (error) {
                log(`擴展狀態檢查失敗: ${error.message}`, 'error');
                updateStatus('extensionStatus', `❌ 擴展狀態異常: ${error.message}`, 'error');
            }
        }
        
        async function testBackgroundConnection() {
            log('測試背景腳本連接...');

            try {
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    log('Chrome 擴展 API 不可用，無法測試背景腳本連接', 'info');
                    updateStatus('extensionStatus', '⚠️ 需要在擴展環境中才能測試背景腳本連接', 'info');
                    return;
                }

                const response = await chrome.runtime.sendMessage({ action: 'ping' });
                log(`背景腳本回應: ${JSON.stringify(response)}`);

                if (response && response.success) {
                    updateStatus('extensionStatus', '✅ 背景腳本連接正常', 'success');
                } else {
                    throw new Error('背景腳本無回應');
                }
            } catch (error) {
                log(`背景腳本連接失敗: ${error.message}`, 'error');
                updateStatus('extensionStatus', `❌ 背景腳本連接失敗: ${error.message}`, 'error');
            }
        }
        
        async function checkStorageData() {
            log('檢查儲存數據...');

            try {
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    log('Chrome 擴展 API 不可用，無法檢查儲存數據', 'info');
                    updateStatus('storageStatus', '⚠️ 需要在擴展環境中才能檢查儲存數據', 'info');
                    return;
                }

                const response = await chrome.runtime.sendMessage({
                    action: 'getData',
                    type: 'all'
                });

                log(`儲存數據回應: ${JSON.stringify(response, null, 2)}`);

                if (response && response.success) {
                    const { collections, tags } = response.data;
                    log(`找到 ${collections.length} 個收集項目`);
                    log(`找到 ${tags.length} 個標籤`);

                    updateStatus('storageStatus',
                        `✅ 數據載入成功 - ${collections.length} 個收集項目, ${tags.length} 個標籤`,
                        'success');
                } else {
                    throw new Error('無法載入儲存數據');
                }
            } catch (error) {
                log(`儲存數據檢查失敗: ${error.message}`, 'error');
                updateStatus('storageStatus', `❌ 儲存數據檢查失敗: ${error.message}`, 'error');
            }
        }
        
        async function createTestData() {
            log('建立測試數據...');

            try {
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    log('Chrome 擴展 API 不可用，無法建立測試數據', 'info');
                    updateStatus('storageStatus', '⚠️ 需要在擴展環境中才能建立測試數據', 'info');
                    return;
                }

                // 建立測試標籤
                const tagResponse = await chrome.runtime.sendMessage({
                    action: 'createTag',
                    data: {
                        name: '測試標籤',
                        color: '#3b82f6',
                        icon: '🧪'
                    }
                });

                if (tagResponse && tagResponse.success) {
                    log('測試標籤建立成功');

                    // 建立測試收集項目
                    const collectionResponse = await chrome.runtime.sendMessage({
                        action: 'saveCollection',
                        data: {
                            text: '這是一個測試收集項目',
                            tagId: tagResponse.data.id,
                            url: window.location.href,
                            title: document.title,
                            timestamp: Date.now()
                        }
                    });

                    if (collectionResponse && collectionResponse.success) {
                        log('測試收集項目建立成功');
                        updateStatus('storageStatus', '✅ 測試數據建立成功', 'success');
                    } else {
                        throw new Error('建立測試收集項目失敗');
                    }
                } else {
                    throw new Error('建立測試標籤失敗');
                }
            } catch (error) {
                log(`建立測試數據失敗: ${error.message}`, 'error');
                updateStatus('storageStatus', `❌ 建立測試數據失敗: ${error.message}`, 'error');
            }
        }
        
        async function clearAllData() {
            log('清除所有數據...');
            
            try {
                // 這裡需要實現清除功能
                log('清除功能需要在背景腳本中實現');
                updateStatus('storageStatus', '⚠️ 清除功能待實現', 'info');
            } catch (error) {
                log(`清除數據失敗: ${error.message}`, 'error');
                updateStatus('storageStatus', `❌ 清除數據失敗: ${error.message}`, 'error');
            }
        }
        
        function checkContentScript() {
            log('檢查內容腳本...');

            try {
                // 檢查全局變量
                if (window.dragTextClassifier) {
                    log('✅ 檢測到 DragTextClassifier 實例');
                    log(`實例類型: ${typeof window.dragTextClassifier}`);

                    // 檢查實例屬性
                    const classifier = window.dragTextClassifier;
                    log(`標籤數量: ${classifier.tags ? classifier.tags.length : '未知'}`);
                    log(`是否正在拖拽: ${classifier.isDragging || false}`);

                    updateStatus('extensionStatus', '✅ 內容腳本已正確注入，拖拽功能應該可用', 'success');
                } else {
                    log('❌ 未檢測到 DragTextClassifier 實例');
                    updateStatus('extensionStatus', '❌ 內容腳本未注入，請檢查擴展安裝狀態', 'error');
                }

                // 檢查事件監聽器
                const selection = window.getSelection();
                log(`當前選中文字: "${selection.toString()}"`);

                // 檢查 DOM 中是否有擴展相關元素
                const extensionElements = document.querySelectorAll('[class*="drag-text"]');
                log(`找到 ${extensionElements.length} 個擴展相關 DOM 元素`);

            } catch (error) {
                log(`檢查內容腳本失敗: ${error.message}`, 'error');
                updateStatus('extensionStatus', `❌ 內容腳本檢查失敗: ${error.message}`, 'error');
            }
        }

        function simulateDragTest() {
            log('模擬拖拽測試...');

            try {
                if (!window.dragTextClassifier) {
                    throw new Error('DragTextClassifier 未載入');
                }

                // 模擬選中文字
                const testText = '測試拖拽文字';
                log(`模擬選中文字: "${testText}"`);

                // 這裡可以添加更多模擬測試
                updateStatus('extensionStatus', '✅ 拖拽測試準備就緒，請手動測試拖拽功能', 'success');

            } catch (error) {
                log(`拖拽測試失敗: ${error.message}`, 'error');
                updateStatus('extensionStatus', `❌ 拖拽測試失敗: ${error.message}`, 'error');
            }
        }

        // 頁面載入時自動檢查
        document.addEventListener('DOMContentLoaded', () => {
            log('調試頁面載入完成');
            checkExtensionStatus();

            // 延遲檢查內容腳本，給擴展時間載入
            setTimeout(() => {
                checkContentScript();
            }, 1000);
        });
    </script>
</body>
</html>
