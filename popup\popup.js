/**
 * 彈出視窗功能
 */

class PopupManager {
  constructor() {
    this.collections = [];
    this.tags = [];
    this.filteredCollections = [];
    this.selectedCollections = new Set();
    this.currentEditingTag = null;
    
    this.init();
  }

  async init() {
    try {
      // 載入數據
      await this.loadData();
      
      // 綁定事件
      this.bindEvents();
      
      // 渲染界面
      this.render();
      
      console.log('Popup initialized');
    } catch (error) {
      console.error('Failed to initialize popup:', error);
      this.showError('初始化失敗');
    }
  }

  async loadData() {
    const response = await chrome.runtime.sendMessage({
      action: 'getData',
      type: 'all'
    });
    
    if (response && response.success) {
      this.collections = response.data.collections || [];
      this.tags = response.data.tags || [];
      this.filteredCollections = [...this.collections];
    }
  }

  bindEvents() {
    // 搜尋功能
    document.getElementById('searchInput').addEventListener('input', this.handleSearch.bind(this));
    document.getElementById('searchBtn').addEventListener('click', this.handleSearch.bind(this));
    
    // 篩選功能
    document.getElementById('tagFilter').addEventListener('change', this.handleFilter.bind(this));
    document.getElementById('sortBy').addEventListener('change', this.handleSort.bind(this));
    
    // 標籤管理
    document.getElementById('addTagBtn').addEventListener('click', this.showTagModal.bind(this));
    
    // 收集項目管理
    document.getElementById('selectAllBtn').addEventListener('click', this.toggleSelectAll.bind(this));
    document.getElementById('deleteSelectedBtn').addEventListener('click', this.deleteSelected.bind(this));
    
    // 設定和匯出
    document.getElementById('settingsBtn').addEventListener('click', this.openSettings.bind(this));
    document.getElementById('exportBtn').addEventListener('click', this.exportData.bind(this));
    
    // 模態框事件
    document.getElementById('closeModalBtn').addEventListener('click', this.hideTagModal.bind(this));
    document.getElementById('cancelBtn').addEventListener('click', this.hideTagModal.bind(this));
    document.getElementById('saveTagBtn').addEventListener('click', this.saveTag.bind(this));
    
    // 顏色和圖示選擇器
    this.bindColorPicker();
    this.bindIconPicker();
    
    // 點擊外部關閉模態框
    document.getElementById('tagModalOverlay').addEventListener('click', (e) => {
      if (e.target.id === 'tagModalOverlay') {
        this.hideTagModal();
      }
    });
  }

  bindColorPicker() {
    const colorInput = document.getElementById('tagColor');
    const colorPresets = document.querySelectorAll('.color-preset');
    
    colorPresets.forEach(preset => {
      preset.addEventListener('click', () => {
        const color = preset.dataset.color;
        colorInput.value = color;
        
        // 更新選中狀態
        colorPresets.forEach(p => p.classList.remove('active'));
        preset.classList.add('active');
      });
    });
  }

  bindIconPicker() {
    const iconInput = document.getElementById('tagIcon');
    const iconPresets = document.querySelectorAll('.icon-preset');
    
    iconPresets.forEach(preset => {
      preset.addEventListener('click', () => {
        const icon = preset.dataset.icon;
        iconInput.value = icon;
        
        // 更新選中狀態
        iconPresets.forEach(p => p.classList.remove('active'));
        preset.classList.add('active');
      });
    });
  }

  render() {
    this.hideLoading();
    this.renderStats();
    this.renderTags();
    this.renderTagFilter();
    this.renderCollections();
    this.updateDeleteButton();
  }

  renderStats() {
    const today = new Date().toDateString();
    const todayCollections = this.collections.filter(item => 
      new Date(item.timestamp).toDateString() === today
    );
    
    document.getElementById('totalCount').textContent = this.collections.length;
    document.getElementById('tagCount').textContent = this.tags.length;
    document.getElementById('todayCount').textContent = todayCollections.length;
  }

  renderTags() {
    const container = document.getElementById('tagsContainer');
    
    if (this.tags.length === 0) {
      container.innerHTML = '<p class="no-tags">還沒有建立任何標籤</p>';
      return;
    }
    
    container.innerHTML = this.tags.map(tag => `
      <div class="tag-item" style="background-color: ${tag.color}20; color: ${tag.color}; border-color: ${tag.color}40;">
        <span>${tag.icon}</span>
        <span>${tag.name}</span>
        <div class="tag-actions">
          <button class="tag-action-btn" onclick="popupManager.editTag('${tag.id}')" title="編輯">
            ✏️
          </button>
          <button class="tag-action-btn" onclick="popupManager.deleteTag('${tag.id}')" title="刪除">
            🗑️
          </button>
        </div>
      </div>
    `).join('');
  }

  renderTagFilter() {
    const select = document.getElementById('tagFilter');
    const currentValue = select.value;
    
    select.innerHTML = '<option value="">所有標籤</option>' +
      this.tags.map(tag => `
        <option value="${tag.id}">${tag.icon} ${tag.name}</option>
      `).join('');
    
    select.value = currentValue;
  }

  renderCollections() {
    const container = document.getElementById('collectionsContainer');
    const emptyState = document.getElementById('emptyState');
    
    if (this.filteredCollections.length === 0) {
      container.style.display = 'none';
      emptyState.style.display = 'block';
      return;
    }
    
    container.style.display = 'block';
    emptyState.style.display = 'none';
    
    container.innerHTML = this.filteredCollections.map(item => {
      const tag = this.tags.find(t => t.id === item.tagId);
      const isSelected = this.selectedCollections.has(item.id);
      
      return `
        <div class="collection-item ${isSelected ? 'selected' : ''}" data-id="${item.id}">
          <div class="collection-header">
            <div class="collection-tag" style="background-color: ${tag?.color || '#6366f1'}20; color: ${tag?.color || '#6366f1'};">
              <span>${tag?.icon || '🏷️'}</span>
              <span>${tag?.name || '未知標籤'}</span>
            </div>
            <div class="collection-actions-btn">
              <button class="tag-action-btn" onclick="popupManager.copyText('${item.id}')" title="複製">
                📋
              </button>
              <button class="tag-action-btn" onclick="popupManager.deleteCollection('${item.id}')" title="刪除">
                🗑️
              </button>
            </div>
          </div>
          <div class="collection-text">${this.escapeHtml(item.text)}</div>
          <div class="collection-meta">
            <span class="collection-url" title="${item.url}">${this.getDomainFromUrl(item.url)}</span>
            <span class="collection-time">${this.formatTime(item.timestamp)}</span>
          </div>
        </div>
      `;
    }).join('');
    
    // 綁定點擊事件
    container.querySelectorAll('.collection-item').forEach(item => {
      item.addEventListener('click', (e) => {
        if (!e.target.closest('.collection-actions-btn')) {
          this.toggleCollectionSelection(item.dataset.id);
        }
      });
    });
  }

  handleSearch() {
    const query = document.getElementById('searchInput').value.toLowerCase().trim();
    this.applyFilters();
  }

  handleFilter() {
    this.applyFilters();
  }

  handleSort() {
    this.applyFilters();
  }

  applyFilters() {
    const searchQuery = document.getElementById('searchInput').value.toLowerCase().trim();
    const tagFilter = document.getElementById('tagFilter').value;
    const sortBy = document.getElementById('sortBy').value;
    
    // 篩選
    this.filteredCollections = this.collections.filter(item => {
      const matchesSearch = !searchQuery || 
        item.text.toLowerCase().includes(searchQuery) ||
        item.title.toLowerCase().includes(searchQuery) ||
        item.url.toLowerCase().includes(searchQuery);
      
      const matchesTag = !tagFilter || item.tagId === tagFilter;
      
      return matchesSearch && matchesTag;
    });
    
    // 排序
    this.filteredCollections.sort((a, b) => {
      switch (sortBy) {
        case 'oldest':
          return new Date(a.timestamp) - new Date(b.timestamp);
        case 'alphabetical':
          return a.text.localeCompare(b.text);
        case 'newest':
        default:
          return new Date(b.timestamp) - new Date(a.timestamp);
      }
    });
    
    this.renderCollections();
  }

  toggleCollectionSelection(id) {
    if (this.selectedCollections.has(id)) {
      this.selectedCollections.delete(id);
    } else {
      this.selectedCollections.add(id);
    }
    
    this.renderCollections();
    this.updateDeleteButton();
  }

  toggleSelectAll() {
    if (this.selectedCollections.size === this.filteredCollections.length) {
      this.selectedCollections.clear();
    } else {
      this.filteredCollections.forEach(item => {
        this.selectedCollections.add(item.id);
      });
    }
    
    this.renderCollections();
    this.updateDeleteButton();
  }

  updateDeleteButton() {
    const btn = document.getElementById('deleteSelectedBtn');
    btn.disabled = this.selectedCollections.size === 0;
  }

  async deleteSelected() {
    if (this.selectedCollections.size === 0) return;
    
    if (!confirm(`確定要刪除 ${this.selectedCollections.size} 個項目嗎？`)) {
      return;
    }
    
    try {
      for (const id of this.selectedCollections) {
        await chrome.runtime.sendMessage({
          action: 'deleteCollection',
          collectionId: id
        });
      }
      
      this.selectedCollections.clear();
      await this.loadData();
      this.applyFilters();
      this.renderStats();
      
      this.showSuccess('刪除成功');
    } catch (error) {
      console.error('Failed to delete collections:', error);
      this.showError('刪除失敗');
    }
  }

  async copyText(id) {
    const item = this.collections.find(c => c.id === id);
    if (item) {
      try {
        await navigator.clipboard.writeText(item.text);
        this.showSuccess('已複製到剪貼簿');
      } catch (error) {
        console.error('Failed to copy text:', error);
        this.showError('複製失敗');
      }
    }
  }

  async deleteCollection(id) {
    if (!confirm('確定要刪除這個項目嗎？')) {
      return;
    }
    
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'deleteCollection',
        collectionId: id
      });
      
      if (response && response.success) {
        await this.loadData();
        this.applyFilters();
        this.renderStats();
        this.showSuccess('刪除成功');
      } else {
        this.showError('刪除失敗');
      }
    } catch (error) {
      console.error('Failed to delete collection:', error);
      this.showError('刪除失敗');
    }
  }

  showTagModal(tag = null) {
    this.currentEditingTag = tag;
    
    const modal = document.getElementById('tagModalOverlay');
    const title = document.getElementById('modalTitle');
    const nameInput = document.getElementById('tagName');
    const colorInput = document.getElementById('tagColor');
    const iconInput = document.getElementById('tagIcon');
    
    if (tag) {
      title.textContent = '編輯標籤';
      nameInput.value = tag.name;
      colorInput.value = tag.color;
      iconInput.value = tag.icon;
    } else {
      title.textContent = '新增標籤';
      nameInput.value = '';
      colorInput.value = '#6366f1';
      iconInput.value = '🏷️';
    }
    
    modal.style.display = 'flex';
    nameInput.focus();
  }

  hideTagModal() {
    document.getElementById('tagModalOverlay').style.display = 'none';
    this.currentEditingTag = null;
  }

  async saveTag() {
    const name = document.getElementById('tagName').value.trim();
    const color = document.getElementById('tagColor').value;
    const icon = document.getElementById('tagIcon').value;
    
    if (!name) {
      this.showError('請輸入標籤名稱');
      return;
    }
    
    try {
      let response;
      
      if (this.currentEditingTag) {
        // 編輯標籤
        response = await chrome.runtime.sendMessage({
          action: 'updateTag',
          tagId: this.currentEditingTag.id,
          data: { name, color, icon }
        });
      } else {
        // 新增標籤
        response = await chrome.runtime.sendMessage({
          action: 'createTag',
          data: { name, color, icon }
        });
      }
      
      if (response && response.success) {
        await this.loadData();
        this.render();
        this.hideTagModal();
        this.showSuccess(this.currentEditingTag ? '標籤更新成功' : '標籤建立成功');
      } else {
        this.showError('操作失敗');
      }
    } catch (error) {
      console.error('Failed to save tag:', error);
      this.showError('操作失敗');
    }
  }

  async editTag(id) {
    const tag = this.tags.find(t => t.id === id);
    if (tag) {
      this.showTagModal(tag);
    }
  }

  async deleteTag(id) {
    if (!confirm('確定要刪除這個標籤嗎？使用此標籤的收集項目將變為未分類。')) {
      return;
    }
    
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'deleteTag',
        tagId: id
      });
      
      if (response && response.success) {
        await this.loadData();
        this.render();
        this.showSuccess('標籤刪除成功');
      } else {
        this.showError('刪除失敗');
      }
    } catch (error) {
      console.error('Failed to delete tag:', error);
      this.showError('刪除失敗');
    }
  }

  async openSettings() {
    try {
      await chrome.runtime.openOptionsPage();
    } catch (error) {
      console.error('Failed to open settings:', error);
    }
  }

  async exportData() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'exportData'
      });
      
      if (response && response.success) {
        const dataStr = JSON.stringify(response.data, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `dragtext-export-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        this.showSuccess('數據匯出成功');
      } else {
        this.showError('匯出失敗');
      }
    } catch (error) {
      console.error('Failed to export data:', error);
      this.showError('匯出失敗');
    }
  }

  // 工具函數
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  getDomainFromUrl(url) {
    try {
      return new URL(url).hostname;
    } catch {
      return url;
    }
  }

  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return '剛剛';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分鐘前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小時前`;
    if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`;
    
    return date.toLocaleDateString('zh-TW');
  }

  showLoading() {
    document.getElementById('loadingState').style.display = 'block';
  }

  hideLoading() {
    document.getElementById('loadingState').style.display = 'none';
  }

  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  showError(message) {
    this.showNotification(message, 'error');
  }

  showNotification(message, type = 'info') {
    // 簡單的通知實現
    console.log(`${type.toUpperCase()}: ${message}`);
  }
}

// 初始化
const popupManager = new PopupManager();
