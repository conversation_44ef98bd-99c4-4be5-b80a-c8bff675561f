<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DragTextClassified 測試頁面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f9fafb;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #1f2937;
            border-bottom: 3px solid #6366f1;
            padding-bottom: 10px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            background: #f9fafb;
        }
        
        .test-section h2 {
            color: #6366f1;
            margin-top: 0;
        }
        
        .highlight {
            background: #fef3c7;
            padding: 2px 4px;
            border-radius: 4px;
        }
        
        .important {
            background: #fee2e2;
            color: #dc2626;
            padding: 10px;
            border-radius: 6px;
            border-left: 4px solid #dc2626;
        }
        
        .success {
            background: #d1fae5;
            color: #065f46;
            padding: 10px;
            border-radius: 6px;
            border-left: 4px solid #10b981;
        }
        
        .code {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .instructions {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #6366f1;
        }

        .draggable-text {
            user-select: text;
            cursor: text;
            padding: 4px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .draggable-text:hover {
            background-color: #f0f9ff;
        }

        .drag-demo {
            background: #eff6ff;
            border: 2px dashed #3b82f6;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }

        .drag-demo h3 {
            color: #1e40af;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏷️ DragTextClassified 測試頁面</h1>
        
        <div class="instructions">
            <h3>📋 測試說明</h3>
            <p>這個頁面用於測試 DragTextClassified Chrome 擴展的功能。請按照以下步驟進行測試：</p>
            
            <div class="step">
                <strong>步驟 1：</strong> 確保擴展已安裝並啟用
            </div>
            <div class="step">
                <strong>步驟 2：</strong> 選中下方任意文字並開始拖拽
            </div>
            <div class="step">
                <strong>步驟 3：</strong> 觀察是否出現拖放界面（左側拖放區域 + 右側資料夾列表）
            </div>
            <div class="step">
                <strong>步驟 4：</strong> 將文字拖放到右側的資料夾項目上
            </div>
            <div class="step">
                <strong>步驟 5：</strong> 點擊擴展圖示查看收集的文字
            </div>
        </div>

        <div class="drag-demo">
            <h3>🎯 拖拽測試區域</h3>
            <p>選中下方文字並拖拽，應該會出現類似附件圖片的界面：</p>
            <div class="draggable-text">
                <strong>測試文字：</strong>這是一段可以拖拽的測試文字，請選中並拖拽它！
            </div>
            <p><small>💡 預期效果：左側顯示拖放區域，右側顯示資料夾列表</small></p>
        </div>

        <div class="test-section">
            <h2>📝 一般文字測試</h2>
            <p class="draggable-text">這是一段普通的文字，您可以選中並拖拽它來測試基本的文字收集功能。這段文字包含了中文字符，可以用來測試擴展對中文的支援程度。</p>

            <p class="draggable-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.</p>
        </div>

        <div class="test-section">
            <h2>🔍 特殊內容測試</h2>
            <p class="draggable-text">這裡有一些<span class="highlight">高亮文字</span>和<strong>粗體文字</strong>，以及<em>斜體文字</em>。</p>

            <div class="important draggable-text">
                ⚠️ 重要提示：這是一個重要的警告訊息，可以用來測試特殊格式文字的收集。
            </div>

            <div class="success draggable-text">
                ✅ 成功訊息：這是一個成功提示，包含特殊的背景色和邊框。
            </div>
        </div>

        <div class="test-section">
            <h2>💻 程式碼測試</h2>
            <p>以下是一段程式碼，可以測試對程式碼文字的收集：</p>
            
            <div class="code">
function dragTextClassifier() {
    const selectedText = window.getSelection().toString();
    if (selectedText.length > 0) {
        showTagSelector(selectedText);
    }
}

// 這是一段 JavaScript 程式碼
console.log("Hello, DragTextClassified!");
            </div>
        </div>

        <div class="test-section">
            <h2>🌐 多語言測試</h2>
            <p><strong>中文：</strong>這是中文文字測試，包含繁體中文字符。</p>
            <p><strong>English:</strong> This is English text for testing multilingual support.</p>
            <p><strong>日本語：</strong>これは日本語のテストテキストです。</p>
            <p><strong>한국어:</strong>이것은 한국어 테스트 텍스트입니다.</p>
            <p><strong>Français:</strong> Ceci est un texte de test en français.</p>
        </div>

        <div class="test-section">
            <h2>📊 數字和符號測試</h2>
            <p>數字測試：1234567890</p>
            <p>特殊符號：!@#$%^&*()_+-=[]{}|;':\",./<>?</p>
            <p>Email: <EMAIL></p>
            <p>URL: https://www.example.com/path?param=value</p>
            <p>電話：+886-2-1234-5678</p>
        </div>

        <div class="test-section">
            <h2>📋 長文字測試</h2>
            <p>這是一段較長的文字，用於測試擴展對長文字的處理能力。在實際使用中，用戶可能會選中很長的段落或文章片段，因此需要確保擴展能夠正確處理這些情況。這段文字包含了多個句子，並且有足夠的長度來測試文字截斷和顯示功能。同時，這段文字也包含了標點符號、數字和特殊字符，可以全面測試擴展的文字處理能力。</p>
        </div>

        <div class="test-section">
            <h2>🎯 功能測試清單</h2>
            <h3>基本功能：</h3>
            <ul>
                <li>✅ 文字選中檢測</li>
                <li>✅ 拖拽動作檢測</li>
                <li>✅ 標籤選擇器顯示</li>
                <li>✅ 標籤選擇功能</li>
                <li>✅ 新標籤建立</li>
                <li>✅ 文字儲存功能</li>
            </ul>
            
            <h3>進階功能：</h3>
            <ul>
                <li>✅ 彈出視窗顯示</li>
                <li>✅ 標籤管理</li>
                <li>✅ 搜尋和篩選</li>
                <li>✅ 數據匯出</li>
                <li>✅ 設定頁面</li>
                <li>✅ 快捷鍵支援</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔧 除錯資訊</h2>
            <p>如果遇到問題，請檢查以下項目：</p>
            <ol>
                <li>確認擴展已正確安裝並啟用</li>
                <li>檢查瀏覽器控制台是否有錯誤訊息</li>
                <li>確認網頁已完全載入</li>
                <li>嘗試重新載入頁面</li>
                <li>檢查擴展權限設定</li>
            </ol>
            
            <p><strong>版本資訊：</strong>DragTextClassified v1.0.0</p>
            <p><strong>測試日期：</strong><span id="testDate"></span></p>
        </div>

        <div class="test-section">
            <h2>🔗 相關測試頁面</h2>
            <ul>
                <li><a href="install-test.html" target="_blank">📦 安裝測試頁面</a> - 檢查擴展是否正確安裝</li>
                <li><a href="interface-preview.html" target="_blank">🎨 界面預覽</a> - 查看新的拖放界面設計</li>
                <li><a href="debug-popup.html" target="_blank">🔧 Popup 調試頁面</a> - 調試 popup 功能和數據載入</li>
            </ul>
        </div>
    </div>

    <script>
        // 顯示當前日期
        document.getElementById('testDate').textContent = new Date().toLocaleDateString('zh-TW');
        
        // 測試用的控制台輸出
        console.log('DragTextClassified 測試頁面已載入');
        console.log('當前 URL:', window.location.href);
        console.log('頁面標題:', document.title);
        
        // 檢測擴展是否已載入
        setTimeout(() => {
            if (window.dragTextClassifier) {
                console.log('✅ DragTextClassified 擴展已檢測到');
            } else {
                console.log('❌ 未檢測到 DragTextClassified 擴展');
            }
        }, 1000);
    </script>
</body>
</html>
