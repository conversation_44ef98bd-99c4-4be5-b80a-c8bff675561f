{"name": "dragtextclassified", "version": "1.0.0", "description": "智能拖拽文字分類收集工具 - Chrome Extension", "main": "manifest.json", "scripts": {"test": "echo \"Opening test page...\" && start test.html", "dev": "echo \"Development mode - Load extension in Chrome\"", "build": "echo \"Building extension for production\"", "lint": "echo \"Linting code...\"", "validate": "echo \"Validating manifest...\"", "zip": "echo \"Creating extension package...\""}, "keywords": ["chrome-extension", "text-collection", "drag-and-drop", "tagging", "productivity"], "author": "DragTextClassified Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/username/DragTextClassified.git"}, "bugs": {"url": "https://github.com/username/DragTextClassified/issues"}, "homepage": "https://github.com/username/DragTextClassified#readme", "devDependencies": {}, "dependencies": {}, "engines": {"node": ">=14.0.0"}, "browserslist": ["Chrome >= 88"]}