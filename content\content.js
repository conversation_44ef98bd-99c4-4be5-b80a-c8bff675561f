/**
 * 內容腳本 - 拖拽文字檢測和標籤選擇
 */

class DragTextClassifier {
  constructor() {
    this.isDragging = false;
    this.selectedText = '';
    this.dragStartPosition = { x: 0, y: 0 };
    this.tagSelector = null;
    this.tags = [];
    this.settings = {};
    
    this.init();
  }

  async init() {
    try {
      // 載入儲存的數據
      await this.loadData();
      
      // 綁定事件監聽器
      this.bindEvents();
      
      console.log('DragTextClassifier initialized');
    } catch (error) {
      console.error('Failed to initialize DragTextClassifier:', error);
    }
  }

  async loadData() {
    // 從背景腳本獲取數據
    const response = await chrome.runtime.sendMessage({
      action: 'getData',
      type: 'all'
    });
    
    if (response && response.success) {
      this.tags = response.data.tags || [];
      this.settings = response.data.settings || {};
    }
  }

  bindEvents() {
    // 監聽拖拽事件
    document.addEventListener('dragstart', this.handleDragStart.bind(this));
    document.addEventListener('dragend', this.handleDragEnd.bind(this));

    // 監聽鍵盤事件
    document.addEventListener('keydown', this.handleKeyDown.bind(this));

    // 監聽來自背景腳本的消息
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));
  }

  handleDragStart(event) {
    // 獲取被拖拽的文字
    const selection = window.getSelection();
    const selectedText = selection.toString().trim();

    if (selectedText && selectedText.length > 0) {
      this.selectedText = selectedText;
      this.isDragging = true;

      // 記錄鼠標位置
      this.dragStartX = event.clientX;
      this.dragStartY = event.clientY;

      // 設定拖拽數據
      event.dataTransfer.setData('text/plain', selectedText);
      event.dataTransfer.effectAllowed = 'copy';

      // 顯示標籤資料夾
      this.showTagFolders();

      console.log('Drag started with text:', selectedText);
    }
  }

  handleDragEnd() {
    this.isDragging = false;

    // 延遲隱藏標籤資料夾，給drop事件時間執行
    // 增加延遲時間，避免過早隱藏
    setTimeout(() => {
      if (!this.isDragging) {
        this.hideTagFolders();
      }
    }, 300);

    console.log('Drag ended');
  }

  handleKeyDown(event) {
    // ESC鍵隱藏標籤資料夾
    if (event.key === 'Escape') {
      this.hideTagFolders();
    }
  }

  handleMessage(request) {
    switch (request.action) {
      case 'updateTags':
        this.tags = request.tags;
        if (this.tagFolders) {
          this.updateTagFolders();
        }
        break;
      case 'updateSettings':
        this.settings = request.settings;
        break;
    }
  }

  showTagFolders() {
    if (this.tagFolders) {
      this.hideTagFolders();
    }

    // 創建標籤資料夾容器
    this.tagFolders = document.createElement('div');
    this.tagFolders.className = 'drag-text-tag-folders';

    // 初始設置為不可見，避免閃爍
    this.tagFolders.style.opacity = '0';
    this.tagFolders.style.pointerEvents = 'none';

    this.tagFolders.innerHTML = this.generateTagFoldersHTML();

    // 添加到頁面
    document.body.appendChild(this.tagFolders);

    // 使用 requestAnimationFrame 確保 DOM 更新後再顯示
    requestAnimationFrame(() => {
      if (this.tagFolders) {
        // 設置位置（避免閃爍到中央）
        this.positionTagFolders();

        this.tagFolders.style.opacity = '1';
        this.tagFolders.style.pointerEvents = 'auto';

        // 綁定拖放事件
        this.bindTagFolderEvents();

        console.log('Tag folders shown');
      }
    });
  }

  generateTagFoldersHTML() {
    const foldersHTML = this.tags.map(tag => `
      <div class="folder-item" data-tag-id="${tag.id}">
        <div class="folder-icon">📁</div>
        <span class="folder-name">${tag.name}</span>
      </div>
    `).join('');

    return `
      <div class="drag-drop-container">
        <div class="drop-zone">
          <div class="drop-zone-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
          </div>
          <div class="drop-zone-text">
            <h3>Drag and drop files here</h3>
            <p>拖拽文字到此區域或選擇右側資料夾</p>
          </div>
          <div class="selected-text-preview" id="selectedTextPreview">
            "${this.selectedText ? this.selectedText.substring(0, 100) + (this.selectedText.length > 100 ? '...' : '') : ''}"
          </div>
        </div>
        <div class="folders-sidebar">
          <div class="folders-list">
            ${foldersHTML}
            <div class="folder-item new-folder" data-tag-id="new">
              <div class="folder-icon">➕</div>
              <span class="folder-name">Create or select folder</span>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  bindTagFolderEvents() {
    // 為拖放區域綁定事件
    const dropZone = this.tagFolders.querySelector('.drop-zone');
    if (dropZone) {
      dropZone.addEventListener('dragover', (event) => {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'copy';
        dropZone.classList.add('drag-over');
      });

      dropZone.addEventListener('dragleave', (event) => {
        // 只有當離開整個drop-zone時才移除高亮
        const rect = dropZone.getBoundingClientRect();
        const x = event.clientX;
        const y = event.clientY;

        // 檢查鼠標是否真的離開了drop-zone區域
        if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
          dropZone.classList.remove('drag-over');
        }
      });

      dropZone.addEventListener('drop', (event) => {
        event.preventDefault();
        dropZone.classList.remove('drag-over');
        // 拖放到主區域時顯示資料夾選擇
        this.showFolderSelection();
      });
    }

    // 為每個資料夾項目綁定事件
    this.tagFolders.querySelectorAll('.folder-item').forEach(folder => {
      // 允許drop
      folder.addEventListener('dragover', (event) => {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'copy';
        folder.classList.add('drag-over');
      });

      // 離開時移除高亮
      folder.addEventListener('dragleave', () => {
        folder.classList.remove('drag-over');
      });

      // 處理drop事件
      folder.addEventListener('drop', (event) => {
        event.preventDefault();
        folder.classList.remove('drag-over');

        const tagId = folder.dataset.tagId;
        const droppedText = event.dataTransfer.getData('text/plain');

        if (tagId === 'new') {
          this.handleDropOnNewFolder(droppedText);
        } else {
          this.handleDropOnFolder(tagId, droppedText);
        }
      });

      // 點擊事件作為備選
      folder.addEventListener('click', () => {
        const tagId = folder.dataset.tagId;
        if (this.selectedText) {
          if (tagId === 'new') {
            this.handleDropOnNewFolder(this.selectedText);
          } else {
            this.handleDropOnFolder(tagId, this.selectedText);
          }
        }
      });
    });
  }

  async handleDropOnFolder(tagId, text) {
    try {
      console.log('Dropped text on folder:', tagId, text);

      // 收集文字數據
      const collectionData = {
        text: text,
        tagId: tagId,
        url: window.location.href,
        title: document.title,
        timestamp: new Date().toISOString()
      };

      // 發送到背景腳本保存
      const response = await chrome.runtime.sendMessage({
        action: 'saveCollection',
        data: collectionData
      });

      if (response && response.success) {
        this.showSuccessNotification();
        this.hideTagFolders();

        // 清除文字選擇
        window.getSelection().removeAllRanges();
      } else {
        this.showErrorNotification('保存失敗');
      }
    } catch (error) {
      console.error('Failed to handle drop:', error);
      this.showErrorNotification('保存失敗');
    }
  }

  showFolderSelection() {
    // 高亮所有資料夾，讓用戶選擇
    const folders = this.tagFolders.querySelectorAll('.folder-item');
    folders.forEach(folder => {
      folder.classList.add('selectable');
    });

    // 顯示提示
    const dropZone = this.tagFolders.querySelector('.drop-zone');
    if (dropZone) {
      dropZone.classList.add('awaiting-selection');
    }
  }

  async handleDropOnNewFolder(text) {
    const tagName = prompt('請輸入新標籤名稱：');
    if (tagName && tagName.trim()) {
      try {
        const response = await chrome.runtime.sendMessage({
          action: 'createTag',
          data: {
            name: tagName.trim(),
            color: this.getRandomColor(),
            icon: '🏷️'
          }
        });

        if (response && response.success) {
          // 更新本地標籤列表
          this.tags = response.data.tags;

          // 重新生成界面
          this.updateTagFolders();

          // 自動將文字保存到新標籤
          await this.handleDropOnFolder(response.data.newTag.id, text);
        } else {
          this.showErrorNotification('建立標籤失敗');
        }
      } catch (error) {
        console.error('Failed to create new tag:', error);
        this.showErrorNotification('建立標籤失敗');
      }
    }
  }

  // 這些函數已被拖放功能取代，保留以防其他地方需要
  async selectTag(tagId) {
    return this.handleDropOnFolder(tagId, this.selectedText);
  }

  getRandomColor() {
    const colors = ['#6366f1', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  adjustSelectorPosition() {
    if (!this.tagSelector) return;

    const rect = this.tagSelector.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 調整水平位置
    if (rect.right > viewportWidth) {
      this.tagSelector.style.left = `${viewportWidth - rect.width - 10}px`;
    }

    // 調整垂直位置
    if (rect.bottom > viewportHeight) {
      this.tagSelector.style.top = `${viewportHeight - rect.height - 10}px`;
    }
  }

  positionTagFolders() {
    if (!this.tagFolders) return;

    const folderWidth = 520;
    const folderHeight = 360;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let left, top;

    if (this.dragStartX && this.dragStartY) {
      // 基於拖拽開始位置計算
      left = this.dragStartX - folderWidth / 2;
      top = this.dragStartY - folderHeight / 2;
    } else {
      // 回退到中央位置
      left = (viewportWidth - folderWidth) / 2;
      top = (viewportHeight - folderHeight) / 2;
    }

    // 確保不超出視窗邊界
    left = Math.max(10, Math.min(left, viewportWidth - folderWidth - 10));
    top = Math.max(10, Math.min(top, viewportHeight - folderHeight - 10));

    this.tagFolders.style.left = `${left}px`;
    this.tagFolders.style.top = `${top}px`;
    this.tagFolders.style.transform = 'none'; // 移除CSS中的transform
  }

  hideTagFolders() {
    if (this.tagFolders) {
      this.tagFolders.remove();
      this.tagFolders = null;
      console.log('Tag folders hidden');
    }
  }

  showSuccessNotification() {
    this.showNotification('文字已成功收集！', 'success');
  }

  showErrorNotification(message) {
    this.showNotification(message, 'error');
  }

  showNotification(message, type = 'info') {
    if (!this.settings.showNotifications) return;

    const notification = document.createElement('div');
    notification.className = `drag-text-notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // 自動移除通知
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 3000);
  }

  updateTagFolders() {
    if (this.tagFolders) {
      this.tagFolders.innerHTML = this.generateTagFoldersHTML();
      this.bindTagFolderEvents();
    }
  }
}

// 初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new DragTextClassifier();
  });
} else {
  new DragTextClassifier();
}
