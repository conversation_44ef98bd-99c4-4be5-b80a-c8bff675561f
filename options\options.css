/**
 * 設定頁面樣式
 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #374151;
  background: #f9fafb;
  min-height: 100vh;
}

.options-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 標題區域 */
.options-header {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  padding: 40px 40px 60px;
  position: relative;
}

.options-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: white;
  border-radius: 30px 30px 0 0;
}

.header-content {
  position: relative;
  z-index: 1;
}

.options-header h1 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-icon {
  font-size: 36px;
}

.subtitle {
  font-size: 18px;
  opacity: 0.9;
  font-weight: 300;
}

/* 主要內容 */
.options-main {
  padding: 20px 40px 40px;
}

/* 設定區段 */
.settings-section {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid #e5e7eb;
}

.settings-section:last-child {
  border-bottom: none;
}

.settings-section h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-section h2::before {
  content: '';
  width: 4px;
  height: 24px;
  background: #6366f1;
  border-radius: 2px;
}

/* 設定項目 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 0;
  border-bottom: 1px solid #f3f4f6;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
  margin-right: 20px;
}

.setting-info label {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  display: block;
  margin-bottom: 4px;
}

.setting-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.setting-control {
  min-width: 200px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.setting-control:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 切換開關 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  cursor: pointer;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #cbd5e1;
  border-radius: 24px;
  transition: all 0.3s;
}

.toggle-slider::before {
  position: absolute;
  content: '';
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background: white;
  border-radius: 50%;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .toggle-slider {
  background: #6366f1;
}

.toggle-switch input:checked + .toggle-slider::before {
  transform: translateX(26px);
}

/* 快捷鍵顯示 */
.shortcut-display {
  display: flex;
  gap: 4px;
  align-items: center;
}

.shortcut-display kbd {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  font-family: monospace;
  color: #374151;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.shortcut-note {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  font-size: 14px;
  color: #0369a1;
}

.shortcut-note a {
  color: #0369a1;
  text-decoration: underline;
}

/* 按鈕樣式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-danger {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.btn-danger:hover {
  background: #fee2e2;
}

/* 匯入控制 */
.import-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 危險區域 */
.danger-zone {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.danger-zone .setting-info label {
  color: #dc2626;
}

/* 關於區域 */
.about-info {
  background: #f9fafb;
  border-radius: 12px;
  padding: 30px;
}

.app-info {
  text-align: center;
  margin-bottom: 30px;
}

.app-info h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.version {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 16px;
}

.description {
  font-size: 16px;
  color: #4b5563;
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto;
}

.links {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.link-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: white;
  color: #374151;
  text-decoration: none;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.link-btn:hover {
  background: #f9fafb;
  border-color: #6366f1;
  color: #6366f1;
}

/* 儲存狀態 */
.save-status {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: #10b981;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-icon {
  font-size: 16px;
}

.status-text {
  font-weight: 500;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .options-container {
    margin: 0;
    box-shadow: none;
  }
  
  .options-header,
  .options-main {
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .options-header h1 {
    font-size: 24px;
  }
  
  .subtitle {
    font-size: 16px;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .setting-info {
    margin-right: 0;
  }
  
  .setting-control {
    min-width: auto;
  }
  
  .links {
    flex-direction: column;
    align-items: center;
  }
  
  .link-btn {
    width: 100%;
    justify-content: center;
    max-width: 200px;
  }
}

/* 深色主題支援 */
@media (prefers-color-scheme: dark) {
  body {
    background: #111827;
    color: #f9fafb;
  }
  
  .options-container {
    background: #1f2937;
  }
  
  .settings-section {
    border-bottom-color: #374151;
  }
  
  .settings-section h2 {
    color: #f9fafb;
  }
  
  .setting-item {
    border-bottom-color: #374151;
  }
  
  .setting-info label {
    color: #f9fafb;
  }
  
  .setting-description {
    color: #9ca3af;
  }
  
  .setting-control {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .about-info {
    background: #374151;
  }
  
  .app-info h3 {
    color: #f9fafb;
  }
  
  .description {
    color: #d1d5db;
  }
  
  .link-btn {
    background: #4b5563;
    color: #f9fafb;
    border-color: #6b7280;
  }
  
  .link-btn:hover {
    background: #6b7280;
  }
}
