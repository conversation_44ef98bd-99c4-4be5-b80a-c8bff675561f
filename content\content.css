/**
 * 拖拽文字分類器樣式
 */

/* 標籤選擇器容器 */
.drag-text-tag-selector {
  position: fixed;
  z-index: 999999;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  min-width: 280px;
  max-width: 320px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  animation: fadeInScale 0.2s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 標籤選擇器標題 */
.tag-selector-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f9fafb;
  border-radius: 12px 12px 0 0;
}

.selected-text-preview {
  color: #6b7280;
  font-size: 12px;
  font-style: italic;
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #9ca3af;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #6b7280;
}

/* 標籤列表 */
.tag-list {
  padding: 8px 0;
  max-height: 300px;
  overflow-y: auto;
}

/* 標籤項目 */
.tag-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s;
  border-left: 4px solid transparent;
}

.tag-item:hover {
  background: #f9fafb;
}

.tag-item:active {
  background: #f3f4f6;
}

.tag-icon {
  margin-right: 10px;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.tag-name {
  color: #374151;
  font-weight: 500;
  flex: 1;
}

/* 新建標籤項目 */
.new-tag-item {
  border-top: 1px solid #f3f4f6;
  margin-top: 4px;
  color: #6366f1;
}

.new-tag-item:hover {
  background: #f0f9ff;
}

.new-tag-item .tag-name {
  color: #6366f1;
  font-weight: 600;
}

/* 通知樣式 */
.drag-text-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000000;
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.drag-text-notification.success {
  background: #10b981;
}

.drag-text-notification.error {
  background: #ef4444;
}

.drag-text-notification.info {
  background: #6366f1;
}

/* 深色主題支援 */
@media (prefers-color-scheme: dark) {
  .drag-text-tag-selector {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .tag-selector-header {
    background: #111827;
    border-bottom-color: #374151;
  }
  
  .selected-text-preview {
    color: #9ca3af;
  }
  
  .close-btn {
    color: #6b7280;
  }
  
  .close-btn:hover {
    background: #374151;
    color: #d1d5db;
  }
  
  .tag-item:hover {
    background: #374151;
  }
  
  .tag-item:active {
    background: #4b5563;
  }
  
  .tag-name {
    color: #f9fafb;
  }
  
  .new-tag-item {
    border-top-color: #374151;
  }
  
  .new-tag-item:hover {
    background: #1e3a8a;
  }
}

/* 響應式設計 */
@media (max-width: 480px) {
  .drag-text-tag-selector {
    min-width: 240px;
    max-width: 280px;
  }
  
  .tag-item {
    padding: 12px 16px;
  }
  
  .tag-icon {
    font-size: 14px;
    width: 18px;
  }
  
  .tag-name {
    font-size: 13px;
  }
}

/* 滾動條樣式 */
.tag-list::-webkit-scrollbar {
  width: 6px;
}

.tag-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.tag-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.tag-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 深色主題滾動條 */
@media (prefers-color-scheme: dark) {
  .tag-list::-webkit-scrollbar-track {
    background: #374151;
  }
  
  .tag-list::-webkit-scrollbar-thumb {
    background: #6b7280;
  }
  
  .tag-list::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
}
