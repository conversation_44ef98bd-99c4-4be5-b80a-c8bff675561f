/**
 * 背景腳本 - Service Worker
 * 處理數據儲存、同步和擴展生命週期管理
 */

// 導入儲存管理器
importScripts('../utils/storage.js');

class BackgroundService {
  constructor() {
    this.storageManager = storageManager;
    this.init();
  }

  async init() {
    try {
      // 初始化儲存
      await this.storageManager.initialize();
      
      // 綁定事件監聽器
      this.bindEvents();
      
      console.log('Background service initialized');
    } catch (error) {
      console.error('Failed to initialize background service:', error);
    }
  }

  bindEvents() {
    // 監聽擴展安裝/更新
    chrome.runtime.onInstalled.addListener(this.handleInstalled.bind(this));

    // 監聽來自內容腳本和彈出視窗的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      // 處理異步消息
      this.handleMessage(request, sender, sendResponse)
        .then(response => {
          console.log('Sending response:', response);
          sendResponse(response);
        })
        .catch(error => {
          console.error('Message handling error:', error);
          sendResponse({ success: false, error: error.message });
        });

      // 返回 true 表示會異步發送回應
      return true;
    });

    // 監聽快捷鍵
    chrome.commands.onCommand.addListener(this.handleCommand.bind(this));

    // 監聽右鍵選單
    chrome.contextMenus.onClicked.addListener(this.handleContextMenu.bind(this));

    // 監聽標籤頁更新
    chrome.tabs.onUpdated.addListener(this.handleTabUpdated.bind(this));
  }

  async handleInstalled(details) {
    console.log('Extension installed/updated:', details);
    
    if (details.reason === 'install') {
      // 首次安裝
      await this.createContextMenus();
      await this.showWelcomeNotification();
    } else if (details.reason === 'update') {
      // 更新
      await this.createContextMenus();
    }
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      console.log('Processing message:', request.action);

      // 簡單測試 ping
      if (request.action === 'ping') {
        console.log('Ping received, sending pong');
        return { success: true, message: 'pong' };
      }

      // 檢查 storageManager 是否可用
      if (!this.storageManager) {
        console.error('StorageManager not initialized');
        return { success: false, error: 'StorageManager not initialized' };
      }

      switch (request.action) {
        case 'getData':
          console.log('Handling getData request');
          return await this.handleGetData(request);

        case 'saveCollection':
          return await this.handleSaveCollection(request);

        case 'createTag':
          return await this.handleCreateTag(request);

        case 'updateTag':
          return await this.handleUpdateTag(request);

        case 'deleteTag':
          return await this.handleDeleteTag(request);

        case 'deleteCollection':
          return await this.handleDeleteCollection(request);

        case 'getCollections':
          return await this.handleGetCollections(request);

        case 'updateSettings':
          return await this.handleUpdateSettings(request);

        case 'exportData':
          return await this.handleExportData(request);

        case 'clearAllData':
          return await this.handleClearAllData(request);

        case 'testWebhook':
          return await this.handleTestWebhook(request);

        default:
          console.warn('Unknown action:', request.action);
          return { success: false, error: 'Unknown action' };
      }
    } catch (error) {
      console.error('Error handling message:', error);
      return { success: false, error: error.message };
    }
  }

  async handleGetData(request) {
    try {
      console.log('Getting data, request type:', request.type);

      const tags = await this.storageManager.getTags();
      console.log('Tags retrieved:', tags.length, 'items');

      const settings = await this.storageManager.getSettings();
      console.log('Settings retrieved:', settings);

      if (request.type === 'all') {
        const collections = await this.storageManager.getCollections();
        console.log('Collections retrieved:', collections.length, 'items');

        const result = {
          success: true,
          data: { tags, settings, collections }
        };
        console.log('Returning all data:', result);
        return result;
      }

      const result = {
        success: true,
        data: { tags, settings }
      };
      console.log('Returning tags and settings:', result);
      return result;

    } catch (error) {
      console.error('Error in handleGetData:', error);
      return { success: false, error: error.message };
    }
  }

  async handleSaveCollection(request) {
    const collection = await this.storageManager.addCollection(request.data);
    
    if (collection) {
      // 通知所有內容腳本更新
      this.notifyContentScripts('collectionAdded', collection);
      
      // 顯示通知（如果啟用）
      const settings = await this.storageManager.getSettings();
      if (settings.showNotifications) {
        await this.showNotification('文字收集成功', `已收集到標籤中`);
      }
      
      return { success: true, data: collection };
    }
    
    return { success: false, error: 'Failed to save collection' };
  }

  async handleCreateTag(request) {
    const newTag = await this.storageManager.addTag(request.data);

    if (newTag) {
      const tags = await this.storageManager.getTags();

      // 重新創建右鍵菜單
      await this.createContextMenus();

      // 通知所有內容腳本更新標籤
      this.notifyContentScripts('updateTags', tags);

      return {
        success: true,
        data: { newTag, tags }
      };
    }

    return { success: false, error: 'Failed to create tag' };
  }

  async handleUpdateTag(request) {
    const updatedTag = await this.storageManager.updateTag(request.tagId, request.data);

    if (updatedTag) {
      const tags = await this.storageManager.getTags();

      // 重新創建右鍵菜單
      await this.createContextMenus();

      this.notifyContentScripts('updateTags', tags);

      return { success: true, data: updatedTag };
    }

    return { success: false, error: 'Failed to update tag' };
  }

  async handleDeleteTag(request) {
    const success = await this.storageManager.deleteTag(request.tagId);

    if (success) {
      const tags = await this.storageManager.getTags();

      // 重新創建右鍵菜單
      await this.createContextMenus();

      this.notifyContentScripts('updateTags', tags);

      return { success: true };
    }

    return { success: false, error: 'Failed to delete tag' };
  }

  async handleDeleteCollection(request) {
    const success = await this.storageManager.deleteCollection(request.collectionId);
    return { success };
  }

  async handleGetCollections(request) {
    const collections = await this.storageManager.getCollections();
    const tags = await this.storageManager.getTags();
    
    return {
      success: true,
      data: { collections, tags }
    };
  }

  async handleUpdateSettings(request) {
    const success = await this.storageManager.setSettings(request.settings);
    
    if (success) {
      // 通知所有內容腳本更新設定
      this.notifyContentScripts('updateSettings', request.settings);
      
      return { success: true };
    }
    
    return { success: false, error: 'Failed to update settings' };
  }

  async handleExportData(request) {
    try {
      const collections = await this.storageManager.getCollections();
      const tags = await this.storageManager.getTags();
      
      const exportData = {
        collections,
        tags,
        exportDate: new Date().toISOString(),
        version: '1.0.0'
      };
      
      return {
        success: true,
        data: exportData
      };
    } catch (error) {
      return { success: false, error: 'Failed to export data' };
    }
  }

  async handleCommand(command) {
    switch (command) {
      case 'open-collection':
        await this.openCollectionPopup();
        break;
      case 'quick-tag':
        await this.triggerQuickTag();
        break;
    }
  }

  async handleContextMenu(info, tab) {
    if (!info.selectionText) return;

    const menuId = info.menuItemId;

    if (menuId === 'collect-text-select') {
      // 顯示標籤選擇界面
      chrome.tabs.sendMessage(tab.id, {
        action: 'collectSelectedText',
        text: info.selectionText
      });
    } else if (menuId.startsWith('collect-to-')) {
      // 直接收集到指定標籤
      const tagId = menuId.replace('collect-to-', '');

      const collectionData = {
        text: info.selectionText,
        tagId: tagId,
        url: tab.url,
        title: tab.title,
        timestamp: new Date().toISOString()
      };

      // 直接保存到背景腳本
      const result = await this.handleSaveCollection({ data: collectionData });

      if (result.success) {
        // 通知內容腳本顯示成功消息
        chrome.tabs.sendMessage(tab.id, {
          action: 'showSuccessNotification',
          message: '文字已收集到標籤'
        });
      } else {
        chrome.tabs.sendMessage(tab.id, {
          action: 'showErrorNotification',
          message: '收集失敗'
        });
      }
    } else if (menuId === 'collect-text-new') {
      // 建立新標籤並收集
      chrome.tabs.sendMessage(tab.id, {
        action: 'collectSelectedTextNewTag',
        text: info.selectionText
      });
    }
  }

  async handleTabUpdated(tabId, changeInfo, tab) {
    if (changeInfo.status === 'complete' && tab.url) {
      // 頁面載入完成，可以注入內容腳本
      try {
        await chrome.tabs.sendMessage(tabId, { action: 'ping' });
      } catch (error) {
        // 內容腳本可能還沒載入，這是正常的
      }
    }
  }

  async createContextMenus() {
    // 清除現有選單
    await chrome.contextMenus.removeAll();

    // 建立主選單
    chrome.contextMenus.create({
      id: 'collect-text-main',
      title: '收集文字到標籤',
      contexts: ['selection']
    });

    // 建立子選單 - 選擇標籤
    chrome.contextMenus.create({
      id: 'collect-text-select',
      parentId: 'collect-text-main',
      title: '選擇標籤...',
      contexts: ['selection']
    });

    // 獲取標籤並建立子選單
    try {
      const tags = await this.storageManager.getTags();

      // 為每個標籤建立子選單項目
      tags.forEach(tag => {
        chrome.contextMenus.create({
          id: `collect-to-${tag.id}`,
          parentId: 'collect-text-main',
          title: `📁 ${tag.name}`,
          contexts: ['selection']
        });
      });

      // 添加分隔線
      chrome.contextMenus.create({
        id: 'separator',
        parentId: 'collect-text-main',
        type: 'separator',
        contexts: ['selection']
      });

      // 建立新標籤選項
      chrome.contextMenus.create({
        id: 'collect-text-new',
        parentId: 'collect-text-main',
        title: '➕ 建立新標籤',
        contexts: ['selection']
      });

    } catch (error) {
      console.error('Failed to create context menus:', error);
    }
  }

  async notifyContentScripts(action, data) {
    try {
      const tabs = await chrome.tabs.query({});
      
      for (const tab of tabs) {
        try {
          await chrome.tabs.sendMessage(tab.id, {
            action,
            [action === 'updateTags' ? 'tags' : 'data']: data
          });
        } catch (error) {
          // 某些標籤頁可能沒有內容腳本，忽略錯誤
        }
      }
    } catch (error) {
      console.error('Failed to notify content scripts:', error);
    }
  }

  async openCollectionPopup() {
    try {
      // 開啟彈出視窗
      await chrome.action.openPopup();
    } catch (error) {
      console.error('Failed to open popup:', error);
    }
  }

  async triggerQuickTag() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab) {
        await chrome.tabs.sendMessage(tab.id, {
          action: 'triggerQuickTag'
        });
      }
    } catch (error) {
      console.error('Failed to trigger quick tag:', error);
    }
  }

  async showNotification(title, message) {
    try {
      if (chrome.notifications && chrome.notifications.create) {
        await chrome.notifications.create({
          type: 'basic',
          iconUrl: '../logo.png',
          title,
          message
        });
      } else {
        console.log('Notification (API not available):', title, '-', message);
      }
    } catch (error) {
      console.error('Failed to show notification:', error);
      console.log('Notification (fallback):', title, '-', message);
    }
  }

  async handleClearAllData(request) {
    try {
      const success = await this.storageManager.clearAll();

      if (success) {
        // 重新初始化預設數據
        await this.storageManager.initialize();

        // 通知所有內容腳本更新
        const tags = await this.storageManager.getTags();
        this.notifyContentScripts('updateTags', tags);

        return { success: true };
      }

      return { success: false, error: 'Failed to clear data' };
    } catch (error) {
      console.error('Error clearing data:', error);
      return { success: false, error: error.message };
    }
  }

  async handleTestWebhook(request) {
    try {
      console.log('Testing webhook connection...');
      const result = await this.storageManager.testWebhook();

      if (result.success) {
        console.log('Webhook test successful:', result);
        return {
          success: true,
          message: 'Webhook連接測試成功',
          response: result.response
        };
      } else {
        console.error('Webhook test failed:', result);
        return {
          success: false,
          error: result.error || result.reason || 'Unknown error',
          message: 'Webhook連接測試失敗'
        };
      }
    } catch (error) {
      console.error('Failed to test webhook:', error);
      return {
        success: false,
        error: error.message,
        message: 'Webhook測試時發生錯誤'
      };
    }
  }
}

  async showWelcomeNotification() {
    await this.showNotification(
      'DragTextClassified 安裝成功！',
      '開始拖拽文字來收集和分類您的內容吧！'
    );
  }
}

// 初始化背景服務
new BackgroundService();
