<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DragTextClassified - 設定</title>
  <link rel="stylesheet" href="options.css">
</head>
<body>
  <div class="options-container">
    <!-- 標題區域 -->
    <header class="options-header">
      <div class="header-content">
        <h1>
          <span class="app-icon">🏷️</span>
          DragTextClassified 設定
        </h1>
        <p class="subtitle">自定義您的文字收集體驗</p>
      </div>
    </header>

    <!-- 設定內容 -->
    <main class="options-main">
      <!-- 一般設定 -->
      <section class="settings-section">
        <h2>一般設定</h2>
        
        <div class="setting-item">
          <div class="setting-info">
            <label for="theme">主題</label>
            <p class="setting-description">選擇界面主題</p>
          </div>
          <select id="theme" class="setting-control">
            <option value="light">淺色主題</option>
            <option value="dark">深色主題</option>
            <option value="auto">跟隨系統</option>
          </select>
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <label for="autoSave">自動儲存</label>
            <p class="setting-description">拖拽文字時自動儲存到選中的標籤</p>
          </div>
          <label class="toggle-switch">
            <input type="checkbox" id="autoSave">
            <span class="toggle-slider"></span>
          </label>
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <label for="showNotifications">顯示通知</label>
            <p class="setting-description">收集文字時顯示成功通知</p>
          </div>
          <label class="toggle-switch">
            <input type="checkbox" id="showNotifications">
            <span class="toggle-slider"></span>
          </label>
        </div>
      </section>

      <!-- 拖拽設定 -->
      <section class="settings-section">
        <h2>拖拽設定</h2>
        
        <div class="setting-item">
          <div class="setting-info">
            <label for="dragSensitivity">拖拽靈敏度</label>
            <p class="setting-description">調整觸發拖拽檢測的距離</p>
          </div>
          <select id="dragSensitivity" class="setting-control">
            <option value="low">低 (20px)</option>
            <option value="medium">中 (10px)</option>
            <option value="high">高 (5px)</option>
          </select>
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <label for="tagSelectorPosition">標籤選擇器位置</label>
            <p class="setting-description">標籤選擇器的顯示位置</p>
          </div>
          <select id="tagSelectorPosition" class="setting-control">
            <option value="cursor">滑鼠位置</option>
            <option value="center">螢幕中央</option>
            <option value="top-right">右上角</option>
          </select>
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <label for="minTextLength">最小文字長度</label>
            <p class="setting-description">觸發收集的最小文字字數</p>
          </div>
          <input type="number" id="minTextLength" min="1" max="100" value="3" class="setting-control">
        </div>
      </section>

      <!-- 快捷鍵設定 -->
      <section class="settings-section">
        <h2>快捷鍵</h2>
        
        <div class="setting-item">
          <div class="setting-info">
            <label>開啟收集界面</label>
            <p class="setting-description">快速開啟文字收集彈出視窗</p>
          </div>
          <div class="shortcut-display">
            <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>D</kbd>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <label>快速標籤選擇</label>
            <p class="setting-description">快速顯示標籤選擇器</p>
          </div>
          <div class="shortcut-display">
            <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>T</kbd>
          </div>
        </div>

        <p class="shortcut-note">
          <strong>注意：</strong>快捷鍵可以在 Chrome 的擴展管理頁面中自定義。
          <a href="#" id="openShortcutsPage">前往設定</a>
        </p>
      </section>

      <!-- 數據管理 -->
      <section class="settings-section">
        <h2>數據管理</h2>
        
        <div class="setting-item">
          <div class="setting-info">
            <label>匯出數據</label>
            <p class="setting-description">將所有收集的文字和標籤匯出為JSON文件</p>
          </div>
          <button id="exportData" class="btn btn-secondary">
            <span>📤</span> 匯出數據
          </button>
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <label>匯入數據</label>
            <p class="setting-description">從JSON文件匯入數據</p>
          </div>
          <div class="import-controls">
            <input type="file" id="importFile" accept=".json" style="display: none;">
            <button id="importData" class="btn btn-secondary">
              <span>📥</span> 選擇文件
            </button>
          </div>
        </div>

        <div class="setting-item danger-zone">
          <div class="setting-info">
            <label>清除所有數據</label>
            <p class="setting-description">永久刪除所有收集的文字和標籤</p>
          </div>
          <button id="clearAllData" class="btn btn-danger">
            <span>🗑️</span> 清除所有數據
          </button>
        </div>
      </section>

      <!-- 關於 -->
      <section class="settings-section">
        <h2>關於</h2>
        
        <div class="about-info">
          <div class="app-info">
            <h3>DragTextClassified</h3>
            <p class="version">版本 1.0.0</p>
            <p class="description">
              智能拖拽文字分類收集工具，讓您輕鬆管理和分類網頁文字內容。
            </p>
          </div>
          
          <div class="links">
            <a href="#" class="link-btn" id="viewSource">
              <span>📋</span> 查看原始碼
            </a>
            <a href="#" class="link-btn" id="reportIssue">
              <span>🐛</span> 回報問題
            </a>
            <a href="#" class="link-btn" id="rateExtension">
              <span>⭐</span> 評分擴展
            </a>
          </div>
        </div>
      </section>
    </main>

    <!-- 儲存狀態 -->
    <div class="save-status" id="saveStatus" style="display: none;">
      <span class="status-icon">✅</span>
      <span class="status-text">設定已儲存</span>
    </div>
  </div>

  <script src="options.js"></script>
</body>
</html>
