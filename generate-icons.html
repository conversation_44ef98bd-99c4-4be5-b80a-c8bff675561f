<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <h1>Icon Generator for DragTextClassified</h1>
    <p>This page generates PNG icons from SVG. Open browser console and run the script.</p>
    
    <canvas id="canvas" style="border: 1px solid #ccc;"></canvas>
    
    <script>
        // SVG content
        const svgContent = `<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
          <circle cx="64" cy="64" r="60" fill="#6366f1" stroke="#4f46e5" stroke-width="2"/>
          <rect x="30" y="35" width="68" height="8" rx="4" fill="white" opacity="0.9"/>
          <rect x="30" y="50" width="55" height="8" rx="4" fill="white" opacity="0.8"/>
          <rect x="30" y="65" width="62" height="8" rx="4" fill="white" opacity="0.7"/>
          <path d="M75 75 L95 75 L105 85 L95 95 L75 95 L75 75 Z" fill="#fbbf24" stroke="#f59e0b" stroke-width="1"/>
          <circle cx="88" cy="85" r="3" fill="#f59e0b"/>
          <path d="M45 85 L55 80 L55 83 L65 83 L65 87 L55 87 L55 90 Z" fill="#10b981" opacity="0.8"/>
        </svg>`;

        function generateIcon(size) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = size;
            canvas.height = size;
            
            const img = new Image();
            const svgBlob = new Blob([svgContent], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.clearRect(0, 0, size, size);
                ctx.drawImage(img, 0, 0, size, size);
                
                // Convert to PNG and download
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = `icon${size}.png`;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    URL.revokeObjectURL(link.href);
                }, 'image/png');
                
                URL.revokeObjectURL(url);
            };
            
            img.src = url;
        }

        // Generate all required sizes
        function generateAllIcons() {
            const sizes = [16, 32, 48, 128];
            sizes.forEach((size, index) => {
                setTimeout(() => generateIcon(size), index * 1000);
            });
        }

        console.log('Run generateAllIcons() to create all icon files');
    </script>
</body>
</html>
