/**
 * 數據儲存管理工具
 * 使用Chrome Storage API進行數據持久化
 */

class StorageManager {
  constructor() {
    this.STORAGE_KEYS = {
      TAGS: 'dragtext_tags',
      COLLECTIONS: 'dragtext_collections',
      SETTINGS: 'dragtext_settings'
    };

    // Webhook配置
    this.WEBHOOK_CONFIG = {
      URL: 'https://n8n.hxm.com.hk/webhook-test/7626f61b-b10c-43db-a3dc-614413e05fb7',
      ENABLED: true,
      TIMEOUT: 10000 // 10秒超時
    };

    // 預設標籤
    this.DEFAULT_TAGS = [
      { id: 'general', name: '一般', color: '#6366f1', icon: '📝' },
      { id: 'important', name: '重要', color: '#ef4444', icon: '⭐' },
      { id: 'research', name: '研究', color: '#10b981', icon: '🔍' },
      { id: 'quotes', name: '引用', color: '#f59e0b', icon: '💬' },
      { id: 'todo', name: '待辦', color: '#8b5cf6', icon: '✅' }
    ];

    // 預設設定
    this.DEFAULT_SETTINGS = {
      theme: 'light',
      autoSave: true,
      showNotifications: true,
      dragSensitivity: 'medium',
      tagSelectorPosition: 'cursor',
      webhookEnabled: true // 新增webhook開關
    };
  }

  /**
   * 初始化儲存
   */
  async initialize() {
    try {
      const existingTags = await this.getTags();
      if (!existingTags || existingTags.length === 0) {
        await this.setTags(this.DEFAULT_TAGS);
      }
      
      const existingSettings = await this.getSettings();
      if (!existingSettings) {
        await this.setSettings(this.DEFAULT_SETTINGS);
      }
      
      console.log('Storage initialized successfully');
    } catch (error) {
      console.error('Failed to initialize storage:', error);
    }
  }

  /**
   * 獲取所有標籤
   */
  async getTags() {
    try {
      const result = await chrome.storage.sync.get(this.STORAGE_KEYS.TAGS);
      return result[this.STORAGE_KEYS.TAGS] || [];
    } catch (error) {
      console.error('Failed to get tags:', error);
      return [];
    }
  }

  /**
   * 設定標籤
   */
  async setTags(tags) {
    try {
      await chrome.storage.sync.set({
        [this.STORAGE_KEYS.TAGS]: tags
      });
      return true;
    } catch (error) {
      console.error('Failed to set tags:', error);
      return false;
    }
  }

  /**
   * 新增標籤
   */
  async addTag(tag) {
    try {
      const tags = await this.getTags();
      const newTag = {
        id: this.generateId(),
        name: tag.name,
        color: tag.color || '#6366f1',
        icon: tag.icon || '📝',
        createdAt: new Date().toISOString()
      };
      tags.push(newTag);
      await this.setTags(tags);

      // 發送到webhook
      const webhookPayload = {
        tag: newTag,
        totalTags: tags.length
      };

      this.sendToWebhook('tag_created', webhookPayload).catch(error => {
        console.error('Webhook send failed for new tag:', error);
      });

      return newTag;
    } catch (error) {
      console.error('Failed to add tag:', error);
      return null;
    }
  }

  /**
   * 更新標籤
   */
  async updateTag(tagId, updates) {
    try {
      const tags = await this.getTags();
      const tagIndex = tags.findIndex(tag => tag.id === tagId);
      if (tagIndex !== -1) {
        tags[tagIndex] = { ...tags[tagIndex], ...updates };
        await this.setTags(tags);
        return tags[tagIndex];
      }
      return null;
    } catch (error) {
      console.error('Failed to update tag:', error);
      return null;
    }
  }

  /**
   * 刪除標籤
   */
  async deleteTag(tagId) {
    try {
      const tags = await this.getTags();
      const filteredTags = tags.filter(tag => tag.id !== tagId);
      await this.setTags(filteredTags);
      return true;
    } catch (error) {
      console.error('Failed to delete tag:', error);
      return false;
    }
  }

  /**
   * 獲取所有收集項目
   */
  async getCollections() {
    try {
      const result = await chrome.storage.local.get(this.STORAGE_KEYS.COLLECTIONS);
      return result[this.STORAGE_KEYS.COLLECTIONS] || [];
    } catch (error) {
      console.error('Failed to get collections:', error);
      return [];
    }
  }

  /**
   * 新增收集項目
   */
  async addCollection(collection) {
    try {
      const collections = await this.getCollections();
      const tags = await this.getTags();

      const newCollection = {
        id: this.generateId(),
        text: collection.text,
        tagId: collection.tagId,
        url: collection.url,
        title: collection.title,
        timestamp: new Date().toISOString(),
        ...collection
      };

      collections.unshift(newCollection); // 新項目放在最前面
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.COLLECTIONS]: collections
      });

      // 發送到webhook
      const tag = tags.find(t => t.id === newCollection.tagId);
      const webhookPayload = {
        collection: newCollection,
        tag: tag || null,
        totalCollections: collections.length
      };

      // 異步發送到webhook，不阻塞主流程
      this.sendToWebhook('store', webhookPayload).catch(error => {
        console.error('Webhook send failed for new collection:', error);
      });

      return newCollection;
    } catch (error) {
      console.error('Failed to add collection:', error);
      return null;
    }
  }

  /**
   * 刪除收集項目
   */
  async deleteCollection(collectionId) {
    try {
      const collections = await this.getCollections();
      const filteredCollections = collections.filter(item => item.id !== collectionId);
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.COLLECTIONS]: filteredCollections
      });
      return true;
    } catch (error) {
      console.error('Failed to delete collection:', error);
      return false;
    }
  }

  /**
   * 獲取設定
   */
  async getSettings() {
    try {
      const result = await chrome.storage.sync.get(this.STORAGE_KEYS.SETTINGS);
      return result[this.STORAGE_KEYS.SETTINGS] || this.DEFAULT_SETTINGS;
    } catch (error) {
      console.error('Failed to get settings:', error);
      return this.DEFAULT_SETTINGS;
    }
  }

  /**
   * 設定設定
   */
  async setSettings(settings) {
    try {
      await chrome.storage.sync.set({
        [this.STORAGE_KEYS.SETTINGS]: settings
      });
      return true;
    } catch (error) {
      console.error('Failed to set settings:', error);
      return false;
    }
  }

  /**
   * 發送數據到webhook
   */
  async sendToWebhook(action, payload) {
    if (!this.WEBHOOK_CONFIG.ENABLED) {
      console.log('Webhook disabled, skipping send');
      return { success: false, reason: 'disabled' };
    }

    const settings = await this.getSettings();
    if (!settings.webhookEnabled) {
      console.log('Webhook disabled in settings, skipping send');
      return { success: false, reason: 'disabled_in_settings' };
    }

    try {
      const data = {
        action: action,
        payload: payload,
        timestamp: new Date().toISOString(),
        source: 'DragTextClassified'
      };

      console.log('Sending to webhook:', data);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.WEBHOOK_CONFIG.TIMEOUT);

      const response = await fetch(this.WEBHOOK_CONFIG.URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const result = await response.json().catch(() => ({}));
        console.log('Webhook response:', result);
        return { success: true, response: result };
      } else {
        console.error('Webhook error:', response.status, response.statusText);
        return { success: false, error: `HTTP ${response.status}: ${response.statusText}` };
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        console.error('Webhook timeout');
        return { success: false, error: 'timeout' };
      }
      console.error('Webhook send failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 測試webhook連接
   */
  async testWebhook() {
    const testPayload = {
      test: true,
      message: 'This is a test from DragTextClassified extension',
      timestamp: new Date().toISOString()
    };

    return await this.sendToWebhook('test', testPayload);
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  /**
   * 清除所有數據
   */
  async clearAll() {
    try {
      await chrome.storage.sync.clear();
      await chrome.storage.local.clear();
      return true;
    } catch (error) {
      console.error('Failed to clear storage:', error);
      return false;
    }
  }
}

// 導出單例
const storageManager = new StorageManager();
