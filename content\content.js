/**
 * 內容腳本 - 拖拽文字檢測和標籤選擇
 */

class DragTextClassifier {
  constructor() {
    this.isDragging = false;
    this.selectedText = '';
    this.dragStartPosition = { x: 0, y: 0 };
    this.tagSelector = null;
    this.tags = [];
    this.settings = {};
    
    this.init();
  }

  async init() {
    try {
      // 載入儲存的數據
      await this.loadData();
      
      // 綁定事件監聽器
      this.bindEvents();
      
      console.log('DragTextClassifier initialized');
    } catch (error) {
      console.error('Failed to initialize DragTextClassifier:', error);
    }
  }

  async loadData() {
    // 從背景腳本獲取數據
    const response = await chrome.runtime.sendMessage({
      action: 'getData',
      type: 'all'
    });
    
    if (response && response.success) {
      this.tags = response.data.tags || [];
      this.settings = response.data.settings || {};
    }
  }

  bindEvents() {
    // 監聽拖拽事件
    document.addEventListener('dragstart', this.handleDragStart.bind(this));
    document.addEventListener('dragend', this.handleDragEnd.bind(this));

    // 監聽鍵盤事件
    document.addEventListener('keydown', this.handleKeyDown.bind(this));

    // 監聽來自背景腳本的消息
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));
  }

  handleDragStart(event) {
    // 獲取被拖拽的文字
    const selection = window.getSelection();
    const selectedText = selection.toString().trim();

    if (selectedText && selectedText.length > 0) {
      this.selectedText = selectedText;
      this.isDragging = true;

      // 設定拖拽數據
      event.dataTransfer.setData('text/plain', selectedText);
      event.dataTransfer.effectAllowed = 'copy';

      // 顯示標籤資料夾
      this.showTagFolders();

      console.log('Drag started with text:', selectedText);
    }
  }

  handleDragEnd(event) {
    this.isDragging = false;

    // 延遲隱藏標籤資料夾，給drop事件時間執行
    setTimeout(() => {
      this.hideTagFolders();
    }, 100);

    console.log('Drag ended');
  }

  handleKeyDown(event) {
    // ESC鍵隱藏標籤選擇器
    if (event.key === 'Escape') {
      this.hideTagSelector();
    }
  }

  handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'updateTags':
        this.tags = request.tags;
        if (this.tagFolders) {
          this.updateTagFolders();
        }
        break;
      case 'updateSettings':
        this.settings = request.settings;
        break;
    }
  }

  showTagFolders() {
    if (this.tagFolders) {
      this.hideTagFolders();
    }

    // 創建標籤資料夾容器
    this.tagFolders = document.createElement('div');
    this.tagFolders.className = 'drag-text-tag-folders';
    this.tagFolders.innerHTML = this.generateTagFoldersHTML();

    // 添加到頁面
    document.body.appendChild(this.tagFolders);

    // 綁定拖放事件
    this.bindTagFolderEvents();

    console.log('Tag folders shown');
  }

  generateTagFoldersHTML() {
    const foldersHTML = this.tags.map(tag => `
      <div class="tag-folder"
           data-tag-id="${tag.id}"
           style="background-color: ${tag.color}">
        <div class="folder-icon">📁</div>
        <div class="folder-content">
          <span class="folder-emoji">${tag.icon}</span>
          <span class="folder-name">${tag.name}</span>
        </div>
        <div class="drop-indicator">放置文字到此</div>
      </div>
    `).join('');

    return `
      <div class="folders-header">
        <span class="drag-hint">🏷️ 拖拽文字到標籤資料夾</span>
      </div>
      <div class="folders-container">
        ${foldersHTML}
        <div class="tag-folder new-folder" data-tag-id="new">
          <div class="folder-icon">➕</div>
          <div class="folder-content">
            <span class="folder-name">建立新標籤</span>
          </div>
          <div class="drop-indicator">放置以建立新標籤</div>
        </div>
      </div>
    `;
  }

  bindTagFolderEvents() {
    // 為每個標籤資料夾綁定拖放事件
    this.tagFolders.querySelectorAll('.tag-folder').forEach(folder => {
      // 允許drop
      folder.addEventListener('dragover', (event) => {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'copy';
        folder.classList.add('drag-over');
      });

      // 離開時移除高亮
      folder.addEventListener('dragleave', (event) => {
        folder.classList.remove('drag-over');
      });

      // 處理drop事件
      folder.addEventListener('drop', (event) => {
        event.preventDefault();
        folder.classList.remove('drag-over');

        const tagId = folder.dataset.tagId;
        const droppedText = event.dataTransfer.getData('text/plain');

        if (tagId === 'new') {
          this.handleDropOnNewFolder(droppedText);
        } else {
          this.handleDropOnFolder(tagId, droppedText);
        }
      });
    });
  }

  async handleDropOnFolder(tagId, text) {
    try {
      console.log('Dropped text on folder:', tagId, text);

      // 收集文字數據
      const collectionData = {
        text: text,
        tagId: tagId,
        url: window.location.href,
        title: document.title,
        timestamp: new Date().toISOString()
      };

      // 發送到背景腳本保存
      const response = await chrome.runtime.sendMessage({
        action: 'saveCollection',
        data: collectionData
      });

      if (response && response.success) {
        this.showSuccessNotification();
        this.hideTagFolders();

        // 清除文字選擇
        window.getSelection().removeAllRanges();
      } else {
        this.showErrorNotification('保存失敗');
      }
    } catch (error) {
      console.error('Failed to handle drop:', error);
      this.showErrorNotification('保存失敗');
    }
  }

  async handleDropOnNewFolder(text) {
    const tagName = prompt('請輸入新標籤名稱：');
    if (tagName && tagName.trim()) {
      try {
        const response = await chrome.runtime.sendMessage({
          action: 'createTag',
          data: {
            name: tagName.trim(),
            color: this.getRandomColor(),
            icon: '🏷️'
          }
        });

        if (response && response.success) {
          // 更新本地標籤列表
          this.tags = response.data.tags;

          // 自動將文字保存到新標籤
          await this.handleDropOnFolder(response.data.newTag.id, text);
        } else {
          this.showErrorNotification('建立標籤失敗');
        }
      } catch (error) {
        console.error('Failed to create new tag:', error);
        this.showErrorNotification('建立標籤失敗');
      }
    }
  }

  // 這些函數已被拖放功能取代，保留以防其他地方需要
  async selectTag(tagId) {
    return this.handleDropOnFolder(tagId, this.selectedText);
  }

  getRandomColor() {
    const colors = ['#6366f1', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  adjustSelectorPosition() {
    if (!this.tagSelector) return;

    const rect = this.tagSelector.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 調整水平位置
    if (rect.right > viewportWidth) {
      this.tagSelector.style.left = `${viewportWidth - rect.width - 10}px`;
    }

    // 調整垂直位置
    if (rect.bottom > viewportHeight) {
      this.tagSelector.style.top = `${viewportHeight - rect.height - 10}px`;
    }
  }

  hideTagFolders() {
    if (this.tagFolders) {
      this.tagFolders.remove();
      this.tagFolders = null;
      console.log('Tag folders hidden');
    }
  }

  showSuccessNotification() {
    this.showNotification('文字已成功收集！', 'success');
  }

  showErrorNotification(message) {
    this.showNotification(message, 'error');
  }

  showNotification(message, type = 'info') {
    if (!this.settings.showNotifications) return;

    const notification = document.createElement('div');
    notification.className = `drag-text-notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // 自動移除通知
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 3000);
  }

  updateTagFolders() {
    if (this.tagFolders) {
      this.tagFolders.innerHTML = this.generateTagFoldersHTML();
      this.bindTagFolderEvents();
    }
  }
}

// 初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new DragTextClassifier();
  });
} else {
  new DragTextClassifier();
}
