# DragTextClassified - Chrome Extension

一個現代化的Chrome擴展程式，讓您可以輕鬆拖拽文字並進行分類收集。

## 功能特色

### 🎯 核心功能
- **檔案管理器風格界面**: 拖拽文字時顯示類似檔案管理器的界面，左側拖放區域，右側資料夾列表
- **真實拖放體驗**: 支援真正的拖放操作，可拖放到左側區域或直接拖放到右側資料夾
- **智能收集**: 自動記錄拖拽的文字內容、來源網頁URL和時間戳
- **標籤管理**: 支援建立、編輯、刪除自定義標籤
- **直觀分類**: 清晰的資料夾列表，一目了然的分類選項
- **歷史記錄**: 完整的文字收集歷史，支援搜尋和篩選

### 🚀 進階功能
- **批量操作**: 支援批量編輯和刪除收集的文字
- **匯出功能**: 支援匯出為JSON、CSV格式
- **同步備份**: 使用Chrome Storage API進行數據同步
- **快捷鍵**: 支援鍵盤快捷鍵操作
- **主題切換**: 支援明暗主題切換

## 技術規格

### 開發環境
- **Manifest Version**: 3 (最新版本)
- **最低Chrome版本**: 88+
- **開發語言**: JavaScript (ES6+), HTML5, CSS3
- **框架**: Vanilla JS (輕量化設計)

### 權限需求
- `activeTab`: 讀取當前活動標籤頁內容
- `storage`: 儲存用戶數據和設定
- `contextMenus`: 右鍵選單功能
- `scripting`: 注入內容腳本

## 安裝方式

### 開發者模式安裝
1. 下載或克隆此專案到本地
2. 確保 `logo.png` 文件存在於專案根目錄
3. 開啟Chrome瀏覽器，進入 `chrome://extensions/`
4. 開啟右上角的「開發者模式」
5. 點擊「載入未封裝項目」
6. 選擇專案資料夾
7. 擴展程式安裝完成

### Chrome Web Store安裝
*即將上架Chrome Web Store*

### 快速測試
安裝完成後，開啟 `test.html` 文件來測試所有功能。

## 使用方法

### 基本操作
1. **拖拽文字**: 在任何網頁上選中並拖拽文字
2. **拖放界面**: 拖拽時會出現檔案管理器風格的界面
   - 左側：拖放區域（顯示選中的文字預覽）
   - 右側：資料夾列表（顯示所有可用標籤）
3. **拖放分類**: 將文字拖放到右側的資料夾項目中
4. **建立新標籤**: 拖放到「Create or select folder」建立自定義標籤
5. **查看收集**: 點擊擴展圖示查看所有收集的文字

### 進階操作
- **右鍵選單**: 右鍵點擊選中的文字，快速分類
- **快捷鍵**: `Ctrl+Shift+D` 快速開啟收集界面
- **搜尋篩選**: 在收集界面使用搜尋功能快速找到內容
- **批量管理**: 選中多個項目進行批量操作

## 專案結構

```
DragTextClassified/
├── manifest.json          # 擴展配置文件
├── popup/                 # 彈出視窗
│   ├── popup.html
│   ├── popup.js
│   └── popup.css
├── content/               # 內容腳本
│   ├── content.js
│   └── content.css
├── background/            # 背景腳本
│   └── background.js
├── options/               # 設定頁面
│   ├── options.html
│   ├── options.js
│   └── options.css
├── assets/                # 資源文件
│   ├── icons/
│   └── images/
├── utils/                 # 工具函數
│   └── storage.js
└── README.md
```

## 開發指南

### 本地開發
```bash
# 克隆專案
git clone <repository-url>
cd DragTextClassified

# 確保logo.png存在
# 將您的logo文件放置在專案根目錄

# 在Chrome中載入擴展
# 1. 前往 chrome://extensions/
# 2. 啟用開發者模式
# 3. 點擊「載入未封裝項目」
# 4. 選擇專案資料夾
```

### 測試
```bash
# 開啟測試頁面
npm test

# 或直接開啟 test.html 文件
```

### 檔案結構說明
- `manifest.json` - 擴展配置文件
- `background/` - 背景腳本（Service Worker）
- `content/` - 內容腳本（注入到網頁）
- `popup/` - 彈出視窗界面
- `options/` - 設定頁面
- `utils/` - 工具函數
- `logo.png` - 擴展圖示（必需）
- `test.html` - 功能測試頁面

## 版本歷史

### v1.0.0 (開發中)
- ✅ 基本拖拽文字收集功能
- ✅ 標籤管理系統
- ✅ 彈出界面設計
- 🔄 數據儲存和同步
- 📋 設定頁面
- 📋 匯出功能

## 貢獻指南

歡迎提交Issue和Pull Request！

### 開發流程
1. Fork此專案
2. 建立功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟Pull Request

## 授權條款

此專案採用 MIT 授權條款 - 詳見 [LICENSE](LICENSE) 文件

## 聯絡方式

- 專案連結: [GitHub Repository](https://github.com/username/DragTextClassified)
- 問題回報: [Issues](https://github.com/username/DragTextClassified/issues)

## 致謝

感謝所有貢獻者和Chrome擴展開發社群的支持！

---

**注意**: 此擴展程式遵循Chrome Web Store的所有政策和最佳實踐，確保用戶隱私和數據安全。
